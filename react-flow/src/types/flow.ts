// React Flow 版本的类型定义
// 基于 Vue 版本的 types/index.d.ts 转换

import type { Node, Edge } from '@xyflow/react';

/**
 * 流程字段定义
 */
export interface FlowField {
  field: string; // 自定义命名
  type?: string; // 字段类型 string number ...
  value?: string; // 自定义值
  name?: string; // 选择的变量名
  nodeId?: string; // 选择的节点ID
  nodeType?: string; // 选择的节点类型 start end ...
  label?: string; // 显示名称
  required?: boolean; // 是否必填
  [key: string]: any;
}

/**
 * 流程数据定义
 */
export interface FlowData {
  inputParams?: FlowField[];
  outputParams?: FlowField[];
  options?: any;
}

/**
 * 表单项定义 (简化版，适配 Ant Design)
 */
export interface FormItem {
  prop: string;
  label: string;
  component: string;
  props?: any;
  required?: boolean;
  rules?: any[];
  [key: string]: any;
}

/**
 * 节点表单配置
 */
export interface NodeForm {
  width?: string;
  focus?: string;
  items: FormItem[];
}

/**
 * 节点连接点配置
 */
export interface NodeHandle {
  target?: boolean;
  source?: boolean;
  next?: { label: string; value: string; [key: string]: any }[];
}

/**
 * 流程节点定义 (扩展 React Flow 的 Node)
 */
export interface FlowNode extends Node {
  enable?: boolean;
  label?: string;
  description?: string;
  icon?: string;
  name?: `node-${string}`;
  form?: NodeForm;
  handle?: NodeHandle;
  data: FlowData;
  color?: string;
  group?: string;
  cardWidth?: string;
  _index?: number;
  validator?(data: FlowData): void | string;
  [key: string]: any;
}

/**
 * 流程连线定义 (扩展 React Flow 的 Edge)
 */
export interface FlowEdge extends Edge {
  animated?: boolean;
  _stroke?: string; // 记录原始颜色
  [key: string]: any;
}

/**
 * 节点运行结果
 */
export interface FlowNodeResult {
  msgType: 'llmStream' | 'tool' | 'node' | 'flow';
  data: {
    status: 'done' | 'running' | 'start' | 'end';
    nodeId: string;
    nodeType: string;
    duration: number;
    name?: string;
    type?: string;
    isEnd: boolean;
    content?: string;
    isThinking?: boolean;
    result?: {
      success: string;
      error: string;
      result?: any;
    };
    count: {
      tokenUsage: number;
    };
    nextNodeIds: string[];
    reason?: 'success' | 'cancel' | 'error';
  };
}

/**
 * 视图配置
 */
export interface FlowViewport {
  x: number;
  y: number;
  zoom: number;
}

/**
 * 流程信息
 */
export interface FlowInfo {
  id?: number;
  name?: string;
  description?: string;
  draft?: {
    nodes: FlowNode[];
    edges: FlowEdge[];
    viewport?: FlowViewport;
    activeNodeId?: string;
  };
  createTime?: string;
  updateTime?: string;
  releaseTime?: string;
  [key: string]: any;
}

/**
 * 节点配置定义
 */
export interface NodeConfig {
  type: string;
  label: string;
  description?: string;
  icon?: string;
  color?: string;
  group?: string;
  form?: NodeForm;
  handle?: NodeHandle;
  data?: FlowData;
  validator?(data: FlowData): void | string;
  component?: React.ComponentType<any>;
}

/**
 * 节点分组
 */
export interface NodeGroup {
  label: string;
  children: NodeConfig[];
}

/**
 * 连接参数
 */
export interface FlowConnection {
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

/**
 * 布局偏移配置
 */
export interface LayoutOffset {
  x: number;
  y_t: number;
  y_b: number;
  g: number;
}

/**
 * 控制模式
 */
export type ControlMode = 'pointer' | 'hand';

/**
 * 运行状态
 */
export type RunStatus = 'idle' | 'running' | 'paused' | 'completed' | 'error';

/**
 * 节点运行状态
 */
export interface NodeRunStatus {
  nodeId: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  startTime?: number;
  endTime?: number;
  result?: any;
  error?: string;
}

/**
 * 流程运行上下文
 */
export interface FlowRunContext {
  flowId?: string;
  status: RunStatus;
  startTime?: number;
  endTime?: number;
  nodeStatuses: Map<string, NodeRunStatus>;
  variables: Map<string, any>;
  logs: FlowNodeResult[];
}

/**
 * 导出的流程数据
 */
export interface ExportFlowData {
  name: string;
  description?: string;
  draft: {
    nodes: FlowNode[];
    edges: FlowEdge[];
    viewport?: FlowViewport;
  };
}
