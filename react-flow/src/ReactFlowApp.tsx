/**
 * AI Flow 编排系统 - 新架构版本
 * 基于新的 useFlow Hook 和节点配置系统
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  ReactFlow,
  Controls,
  MiniMap,
  Background,
  Panel,
  ReactFlowProvider,
  type Connection,
} from '@xyflow/react';
import { Button, Space, message, Drawer, Tooltip } from 'antd';
import {
  SaveOutlined,
  PlayCircleOutlined,
  DownloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';

// 导入 React Flow 样式
import '@xyflow/react/dist/style.css';

// 导入新的架构
import { useFlow } from './hooks';
import { nodeTypes, nodeConfigs } from './config/nodes';
import type { Node } from '@xyflow/react';

// 本地类型定义
interface FlowField {
  field: string;
  type?: string;
  value?: string;
  name?: string;
  nodeId?: string;
}

interface FlowData {
  inputParams?: FlowField[];
  outputParams?: FlowField[];
  options?: any;
}

interface FlowNode extends Node {
  enable?: boolean;
  label?: string;
  description?: string;
  icon?: string;
  name?: `node-${string}`;
  data: FlowData;
}

// 节点配置面板组件
const NodeConfigPanel = ({
  node,
  onClose,
  onUpdate
}: {
  node: FlowNode | null;
  onClose: () => void;
  onUpdate: (nodeId: string, data: any) => void;
}) => {
  if (!node) return null;

  const handleUpdate = (field: string, value: any) => {
    onUpdate(node.id, {
      data: {
        ...node.data,
        options: {
          ...node.data.options,
          [field]: value
        }
      }
    });
  };

  const getNodeTitle = (type: string) => {
    const config = nodeConfigs.find(c => c.type === type);
    return config ? `${config.icon} ${config.label}节点` : `${type} 节点`;
  };

  return (
    <Drawer
      title={getNodeTitle(node.type)}
      placement="right"
      onClose={onClose}
      open={true}
      width={350}
    >
      <div style={{ padding: '16px' }}>
        {/* 基础配置 */}
        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
            节点名称:
          </label>
          <input
            type="text"
            value={node.data.options?.name || ''}
            onChange={(e) => handleUpdate('name', e.target.value)}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px'
            }}
            placeholder="输入节点名称"
          />
        </div>

        {/* LLM节点特殊配置 */}
        {node.type === 'llm' && (
          <>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                模型:
              </label>
              <select
                value={node.data.options?.model || 'gpt-3.5-turbo'}
                onChange={(e) => handleUpdate('model', e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px'
                }}
              >
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="gpt-4">GPT-4</option>
                <option value="gpt-4-turbo">GPT-4 Turbo</option>
                <option value="claude-3">Claude-3</option>
              </select>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                提示词:
              </label>
              <textarea
                value={node.data.options?.prompt || ''}
                onChange={(e) => handleUpdate('prompt', e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                  height: '120px'
                }}
                placeholder="输入提示词模板"
              />
            </div>
          </>
        )}

        {/* 其他节点类型的配置可以在这里添加 */}
      </div>
    </Drawer>
  );
};

// 主要的 ReactFlowApp 组件
function ReactFlowApp() {
  // 使用新的 useFlow Hook
  const flow = useFlow();

  // 选中的节点（用于配置面板）
  const [configNode, setConfigNode] = useState<FlowNode | null>(null);

  // 初始化节点配置
  useEffect(() => {
    nodeConfigs.forEach(config => {
      flow.registerNodeType(config);
    });

    // 初始化默认节点
    flow.initDefault();
  }, [flow]);

  // 处理连线
  const onConnect = useCallback((params: Connection) => {
    flow.addEdge(params);
  }, [flow]);

  // 处理节点点击
  const onNodeClick = useCallback((_: React.MouseEvent, node: FlowNode) => {
    flow.setSelectedNode(node);
    setConfigNode(node);
  }, [flow]);

  // 处理画布点击
  const onPaneClick = useCallback(() => {
    flow.setSelectedNode(undefined);
    setConfigNode(null);
  }, [flow]);

  // 添加节点
  const handleAddNode = useCallback((type: string) => {
    const node = flow.addNode(type, {
      position: {
        x: Math.random() * 400 + 200,
        y: Math.random() * 400 + 200
      }
    });
    if (node) {
      setConfigNode(node);
    }
  }, [flow]);

  // 更新节点
  const handleUpdateNode = useCallback((nodeId: string, data: any) => {
    flow.updateNode(nodeId, data);
  }, [flow]);

  // 保存流程
  const handleSave = useCallback(() => {
    console.log('保存流程:', { nodes: flow.nodes, edges: flow.edges });
    message.success('流程已保存');
  }, [flow.nodes, flow.edges]);

  // 运行流程
  const handleRun = useCallback(() => {
    console.log('运行流程:', { nodes: flow.nodes, edges: flow.edges });
    message.info('开始运行流程...');
  }, [flow.nodes, flow.edges]);

  return (
    <div style={{ width: '100vw', height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏 */}
      <div style={{
        height: '60px',
        background: '#fff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 24px',
        borderBottom: '1px solid #e8e8e8',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ margin: 0, fontSize: '20px', fontWeight: 600 }}>
          🚀 AI Flow 编排系统 - 新架构版本
        </h1>

        <Space size="middle">
          <Tooltip title="保存流程 (Ctrl+S)">
            <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
              保存
            </Button>
          </Tooltip>

          <Tooltip title="运行流程">
            <Button icon={<PlayCircleOutlined />} onClick={handleRun}>
              运行
            </Button>
          </Tooltip>

          <Tooltip title="导出流程">
            <Button icon={<DownloadOutlined />} onClick={() => message.info('导出功能开发中')}>
              导出
            </Button>
          </Tooltip>

          <Tooltip title="导入流程">
            <Button icon={<UploadOutlined />} onClick={() => message.info('导入功能开发中')}>
              导入
            </Button>
          </Tooltip>
        </Space>
      </div>

      {/* 主要内容区域 */}
      <div style={{ flex: 1, position: 'relative' }}>
        <ReactFlow
          nodes={flow.nodes}
          edges={flow.edges}
          onNodesChange={flow.onNodesChange}
          onEdgesChange={flow.onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          onPaneClick={onPaneClick}
          nodeTypes={nodeTypes}
          fitView
        >
          {/* 节点添加面板 */}
          <Panel position="top-left">
            <div style={{
              background: 'white',
              padding: '16px',
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              maxWidth: '200px'
            }}>
              <div style={{ marginBottom: '12px', fontWeight: 'bold', fontSize: '14px' }}>
                🛠️ 节点工具箱
              </div>

              <div style={{ marginBottom: '12px' }}>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>基础节点</div>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Button
                    size="small"
                    onClick={() => handleAddNode('start')}
                    block
                    style={{ textAlign: 'left', height: '32px' }}
                  >
                    🚀 开始节点
                  </Button>
                  <Button
                    size="small"
                    onClick={() => handleAddNode('end')}
                    block
                    style={{ textAlign: 'left', height: '32px' }}
                  >
                    🏁 结束节点
                  </Button>
                </Space>
              </div>

              <div style={{ marginBottom: '12px' }}>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>AI节点</div>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Button
                    size="small"
                    onClick={() => handleAddNode('llm')}
                    block
                    style={{ textAlign: 'left', height: '32px' }}
                  >
                    🤖 LLM节点
                  </Button>
                </Space>
              </div>
            </div>
          </Panel>

          <Controls />
          <MiniMap />
          <Background variant="dots" gap={12} size={1} />
        </ReactFlow>

        {/* 节点配置面板 */}
        <NodeConfigPanel
          node={configNode}
          onClose={() => setConfigNode(null)}
          onUpdate={handleUpdateNode}
        />
      </div>
    </div>
  );
}

// 包装组件，提供 ReactFlowProvider
const ReactFlowAppWrapper = () => {
  return (
    <ReactFlowProvider>
      <ReactFlowApp />
    </ReactFlowProvider>
  );
};

export default ReactFlowAppWrapper;