import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { Layout, Menu, Button, Space, message } from 'antd';
import { 
  AppstoreOutlined, 
  UnorderedListOutlined, 
  SettingOutlined,
  PlayCircleOutlined 
} from '@ant-design/icons';

// 导入 Flow 模块
import { 
  FlowComponent, 
  FlowList, 
  FlowInfo, 
  I18nProvider, 
  useI18n,
  flowConfig 
} from './modules/flow';

const { Header, Sider, Content } = Layout;

// 测试页面组件
const FlowTestPage: React.FC = () => {
  const { t, locale, setLocale } = useI18n();
  const [collapsed, setCollapsed] = useState(false);

  const handleLanguageChange = () => {
    const newLocale = locale === 'zh-cn' ? 'en' : 'zh-cn';
    setLocale(newLocale);
    message.success(`Language changed to ${newLocale}`);
  };

  const handleConfigTest = () => {
    console.log('Current config:', flowConfig.getAll());
    flowConfig.set('debug', !flowConfig.get('debug'));
    message.info(`Debug mode: ${flowConfig.get('debug') ? 'ON' : 'OFF'}`);
  };

  const menuItems = [
    {
      key: 'flow-design',
      icon: <AppstoreOutlined />,
      label: <Link to="/flow/design">{t('编排')}</Link>
    },
    {
      key: 'flow-list',
      icon: <UnorderedListOutlined />,
      label: <Link to="/flow/list">{t('流程列表', {}, { '流程列表': 'Flow List' })}</Link>
    },
    {
      key: 'flow-info',
      icon: <PlayCircleOutlined />,
      label: <Link to="/flow/info?id=1">{t('流程详情', {}, { '流程详情': 'Flow Detail' })}</Link>
    }
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        theme="light"
      >
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(0,0,0,0.1)', 
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontWeight: 'bold'
        }}>
          {collapsed ? 'RF' : 'React Flow'}
        </div>
        <Menu
          mode="inline"
          defaultSelectedKeys={['flow-design']}
          items={menuItems}
        />
      </Sider>
      
      <Layout>
        <Header style={{ 
          padding: '0 16px', 
          background: '#fff', 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <h2 style={{ margin: 0 }}>React Flow Test App</h2>
          <Space>
            <Button onClick={handleLanguageChange}>
              {locale === 'zh-cn' ? 'English' : '中文'}
            </Button>
            <Button icon={<SettingOutlined />} onClick={handleConfigTest}>
              {t('配置')}
            </Button>
          </Space>
        </Header>
        
        <Content style={{ margin: 0, background: '#f0f2f5' }}>
          <Routes>
            <Route path="/" element={<FlowDesignPage />} />
            <Route path="/flow/design" element={<FlowDesignPage />} />
            <Route path="/flow/list" element={<FlowListPage />} />
            <Route path="/flow/info" element={<FlowInfoPage />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
};

// 流程设计页面
const FlowDesignPage: React.FC = () => {
  const { t } = useI18n();
  
  return (
    <div style={{ height: '100%', padding: 0 }}>
      <div style={{ 
        padding: '16px', 
        background: '#fff', 
        borderBottom: '1px solid #f0f0f0',
        marginBottom: 0
      }}>
        <h3 style={{ margin: 0 }}>{t('流程设计器', {}, { '流程设计器': 'Flow Designer' })}</h3>
        <p style={{ margin: '8px 0 0 0', color: '#666' }}>
          {t('在这里可以设计和编辑流程', {}, { '在这里可以设计和编辑流程': 'Design and edit flows here' })}
        </p>
      </div>
      <div style={{ height: 'calc(100vh - 120px)' }}>
        <FlowComponent flowId={1} />
      </div>
    </div>
  );
};

// 流程列表页面
const FlowListPage: React.FC = () => {
  const { t } = useI18n();
  
  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '16px' }}>
        <h3>{t('流程列表', {}, { '流程列表': 'Flow List' })}</h3>
        <p style={{ color: '#666' }}>
          {t('管理所有流程', {}, { '管理所有流程': 'Manage all flows' })}
        </p>
      </div>
      <FlowList />
    </div>
  );
};

// 流程详情页面
const FlowInfoPage: React.FC = () => {
  const { t } = useI18n();
  
  return (
    <div style={{ height: '100%', padding: 0 }}>
      <div style={{ 
        padding: '16px', 
        background: '#fff', 
        borderBottom: '1px solid #f0f0f0',
        marginBottom: 0
      }}>
        <h3 style={{ margin: 0 }}>{t('流程详情', {}, { '流程详情': 'Flow Detail' })}</h3>
        <p style={{ margin: '8px 0 0 0', color: '#666' }}>
          {t('查看和编辑流程详情', {}, { '查看和编辑流程详情': 'View and edit flow details' })}
        </p>
      </div>
      <div style={{ height: 'calc(100vh - 120px)' }}>
        <FlowInfo />
      </div>
    </div>
  );
};

// 主应用组件
const FlowTestApp: React.FC = () => {
  return (
    <I18nProvider initialLocale="zh-cn">
      <Router>
        <FlowTestPage />
      </Router>
    </I18nProvider>
  );
};

export default FlowTestApp;
