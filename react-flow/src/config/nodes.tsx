import React from 'react';

// 本地类型定义
interface FlowField {
  field: string;
  type?: string;
  value?: string;
  name?: string;
  nodeId?: string;
}

interface FlowData {
  inputParams?: FlowField[];
  outputParams?: FlowField[];
  options?: any;
}

interface NodeConfig {
  type: string;
  label: string;
  description?: string;
  icon?: string;
  color?: string;
  defaultData?: FlowData;
}

// 节点组件定义
const NodeComponents = {
  start: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#409eff',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #409eff',
      boxShadow: '0 2px 8px rgba(64, 158, 255, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🚀 开始</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.name || '开始节点'}
      </div>
    </div>
  ),

  end: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#f56c6c',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #f56c6c',
      boxShadow: '0 2px 8px rgba(245, 108, 108, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🏁 结束</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.name || '结束节点'}
      </div>
    </div>
  ),

  llm: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#6172F3',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #6172F3',
      boxShadow: '0 2px 8px rgba(97, 114, 243, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🤖 LLM</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.options?.model || 'gpt-3.5-turbo'}
      </div>
    </div>
  ),

  judge: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#f56c6c',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #f56c6c',
      boxShadow: '0 2px 8px rgba(245, 108, 108, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🔀 条件判断</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.options?.field || '判断条件'}
      </div>
    </div>
  ),

  code: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#67c23a',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #67c23a',
      boxShadow: '0 2px 8px rgba(103, 194, 58, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>💻 代码执行</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.options?.language || 'JavaScript'}
      </div>
    </div>
  ),

  know: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#e6a23c',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #e6a23c',
      boxShadow: '0 2px 8px rgba(230, 162, 60, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>📚 知识库</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.options?.knowledgeBase || '默认知识库'}
      </div>
    </div>
  ),

  classify: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#909399',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #909399',
      boxShadow: '0 2px 8px rgba(144, 147, 153, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🏷️ 分类</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.options?.classifier || '文本分类'}
      </div>
    </div>
  ),

  flow: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#8e44ad',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #8e44ad',
      boxShadow: '0 2px 8px rgba(142, 68, 173, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🔄 子流程</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.options?.flowName || '子流程'}
      </div>
    </div>
  ),

  json: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#17a2b8',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #17a2b8',
      boxShadow: '0 2px 8px rgba(23, 162, 184, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>📄 JSON</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.options?.operation || 'JSON处理'}
      </div>
    </div>
  ),

  parse: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#fd7e14',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #fd7e14',
      boxShadow: '0 2px 8px rgba(253, 126, 20, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🔍 解析</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.options?.parseType || '文本解析'}
      </div>
    </div>
  ),

  variable: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#6f42c1',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #6f42c1',
      boxShadow: '0 2px 8px rgba(111, 66, 193, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>📝 变量</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.options?.variableName || '变量设置'}
      </div>
    </div>
  )
};

// 节点配置定义
export const nodeConfigs: NodeConfig[] = [
  {
    type: 'start',
    label: '开始',
    description: '流程开始节点',
    icon: '🚀',
    color: '#409eff',
    group: '基础节点',
    component: NodeComponents.start,
    handle: {
      target: false,
      source: true,
      next: [{ label: '下一步', value: 'source' }]
    },
    data: {
      inputParams: [],
      outputParams: [],
      options: { name: '开始' }
    },
    form: {
      width: '400px',
      items: [
        {
          prop: 'name',
          label: '节点名称',
          component: 'el-input',
          required: true
        },
        {
          prop: 'description',
          label: '描述',
          component: 'el-input',
          props: { type: 'textarea', rows: 3 }
        }
      ]
    }
  },
  {
    type: 'end',
    label: '结束',
    description: '流程结束节点',
    icon: '🏁',
    color: '#f56c6c',
    group: '基础节点',
    component: NodeComponents.end,
    handle: {
      target: true,
      source: false
    },
    data: {
      inputParams: [],
      outputParams: [],
      options: { name: '结束' }
    },
    form: {
      width: '400px',
      items: [
        {
          prop: 'name',
          label: '节点名称',
          component: 'el-input',
          required: true
        },
        {
          prop: 'outputFormat',
          label: '输出格式',
          component: 'el-select',
          props: {
            options: [
              { label: 'JSON', value: 'json' },
              { label: '纯文本', value: 'text' },
              { label: 'Markdown', value: 'markdown' }
            ]
          }
        }
      ]
    }
  },
  {
    type: 'llm',
    label: 'LLM',
    description: '大语言模型节点',
    icon: '🤖',
    color: '#6172F3',
    group: 'AI节点',
    component: NodeComponents.llm,
    handle: {
      target: true,
      source: true,
      next: [{ label: '下一步', value: 'source' }]
    },
    data: {
      inputParams: [],
      outputParams: [],
      options: {
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 1000,
        prompt: '你是一个智能助手，请回答用户的问题。'
      }
    },
    form: {
      width: '500px',
      items: [
        {
          prop: 'model',
          label: '模型',
          component: 'el-select',
          required: true,
          props: {
            options: [
              { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
              { label: 'GPT-4', value: 'gpt-4' },
              { label: 'GPT-4 Turbo', value: 'gpt-4-turbo' },
              { label: 'Claude-3', value: 'claude-3' }
            ]
          }
        },
        {
          prop: 'temperature',
          label: '温度 (0-2)',
          component: 'el-input-number',
          props: { min: 0, max: 2, step: 0.1 }
        },
        {
          prop: 'maxTokens',
          label: '最大令牌数',
          component: 'el-input-number',
          props: { min: 1, max: 4000 }
        },
        {
          prop: 'prompt',
          label: '提示词',
          component: 'el-input',
          props: { type: 'textarea', rows: 6 }
        }
      ]
    }
  }
  // 其他节点配置将在后续任务中添加
];

// 导出节点类型映射
export const nodeTypes = Object.keys(NodeComponents).reduce((acc, key) => {
  acc[key] = NodeComponents[key as keyof typeof NodeComponents];
  return acc;
}, {} as Record<string, React.ComponentType<any>>);
