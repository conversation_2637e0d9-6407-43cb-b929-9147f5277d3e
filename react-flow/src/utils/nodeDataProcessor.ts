interface FlowField {
  field: string;
  type?: string;
  value?: string;
  name?: string;
  nodeId?: string;
  nodeType?: string;
  label?: string;
  required?: boolean;
  [key: string]: any;
}

interface FlowData {
  inputParams?: FlowField[];
  outputParams?: FlowField[];
  options?: any;
  [key: string]: any;
}

interface FlowNode {
  id: string;
  type: string;
  data: FlowData;
  position: { x: number; y: number };
  [key: string]: any;
}

interface FlowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  [key: string]: any;
}

export class NodeDataProcessor {
  /**
   * 提取传给后端的数据（基于 Vue 版本的 extractData 函数）
   */
  static extractData(nodes: FlowNode[], edges: FlowEdge[]) {
    const nodesResult = nodes.map(node => {
      const processedNode = { ...node };

      // 开始节点特殊处理
      if (node.type === 'start') {
        processedNode.data?.inputParams?.forEach(param => {
          param.name = param.field;
          param.nodeId = node.id;
          param.nodeType = 'start';
        });
      }
      // 其他节点处理
      else {
        ['inputParams', 'outputParams'].forEach(key => {
          if (processedNode.data?.[key]) {
            processedNode.data[key].forEach((field: FlowField) => {
              if (field.nodeId) {
                const sourceNode = nodes.find(n => n.id === field.nodeId);
                if (sourceNode) {
                  field.nodeType = sourceNode.type;
                }
              }
            });
          }
        });
      }

      // 移除前端特有的属性
      return {
        ...processedNode,
        component: undefined,
        form: undefined
      };
    });

    const edgesResult = edges.map(edge => ({
      ...edge,
      animated: false,
      style: {}
    }));

    return {
      nodes: nodesResult,
      edges: edgesResult
    };
  }

  /**
   * 还原节点数据（从后端数据还原到前端格式）
   */
  static restoreData(data: { nodes: FlowNode[]; edges: FlowEdge[] }) {
    const { nodes, edges } = data;

    const restoredNodes = nodes.map(node => ({
      ...node,
      data: {
        ...node.data,
        // 确保必要的字段存在
        inputParams: node.data.inputParams || [],
        outputParams: node.data.outputParams || [],
        options: node.data.options || {}
      }
    }));

    const restoredEdges = edges.map(edge => ({
      ...edge,
      animated: false,
      style: {}
    }));

    return {
      nodes: restoredNodes,
      edges: restoredEdges
    };
  }

  /**
   * 验证节点数据完整性
   */
  static validateNodeData(node: FlowNode): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 基本字段验证
    if (!node.id) {
      errors.push('节点缺少ID');
    }

    if (!node.type) {
      errors.push('节点缺少类型');
    }

    if (!node.data) {
      errors.push('节点缺少数据');
      return { isValid: false, errors };
    }

    // 根据节点类型进行特定验证
    switch (node.type) {
      case 'start':
        if (!node.data.inputParams || node.data.inputParams.length === 0) {
          errors.push('开始节点至少需要一个输入参数');
        }
        break;

      case 'end':
        if (!node.data.outputParams || node.data.outputParams.length === 0) {
          errors.push('结束节点至少需要一个输出参数');
        }
        // 验证输出参数是否绑定了变量
        node.data.outputParams?.forEach((param, index) => {
          if (!param.nodeId) {
            errors.push(`结束节点输出参数 ${param.field} 未绑定变量`);
          }
        });
        break;

      case 'llm':
        if (!node.data.model?.params?.model) {
          errors.push('LLM节点缺少模型配置');
        }
        if (!node.data.prompt) {
          errors.push('LLM节点缺少提示词');
        }
        break;

      case 'judge':
        if (!node.data.options?.IF || node.data.options.IF.length === 0) {
          errors.push('判断节点缺少判断条件');
        }
        break;

      case 'code':
        if (!node.data.options?.code) {
          errors.push('代码节点缺少代码内容');
        }
        break;

      case 'http':
        if (!node.data.options?.url) {
          errors.push('HTTP节点缺少URL配置');
        }
        if (!node.data.options?.method) {
          errors.push('HTTP节点缺少请求方法');
        }
        break;

      case 'knowledge':
        if (!node.data.options?.query) {
          errors.push('知识库节点缺少查询内容');
        }
        break;
    }

    // 验证输入参数绑定
    if (node.type !== 'start' && node.data.inputParams) {
      node.data.inputParams.forEach((param, index) => {
        if (!param.nodeId && param.field) {
          errors.push(`输入参数 ${param.field} 未绑定到其他节点`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取节点的输入参数
   */
  static getNodeInputParams(node: FlowNode): FlowField[] {
    return node.data.inputParams || [];
  }

  /**
   * 获取节点的输出参数
   */
  static getNodeOutputParams(node: FlowNode): FlowField[] {
    return node.data.outputParams || [];
  }

  /**
   * 设置节点的输入参数
   */
  static setNodeInputParams(node: FlowNode, params: FlowField[]): FlowNode {
    return {
      ...node,
      data: {
        ...node.data,
        inputParams: params
      }
    };
  }

  /**
   * 设置节点的输出参数
   */
  static setNodeOutputParams(node: FlowNode, params: FlowField[]): FlowNode {
    return {
      ...node,
      data: {
        ...node.data,
        outputParams: params
      }
    };
  }

  /**
   * 获取节点的配置选项
   */
  static getNodeOptions(node: FlowNode): any {
    return node.data.options || {};
  }

  /**
   * 设置节点的配置选项
   */
  static setNodeOptions(node: FlowNode, options: any): FlowNode {
    return {
      ...node,
      data: {
        ...node.data,
        options: {
          ...node.data.options,
          ...options
        }
      }
    };
  }

  /**
   * 克隆节点数据
   */
  static cloneNode(node: FlowNode): FlowNode {
    return JSON.parse(JSON.stringify(node));
  }

  /**
   * 合并节点数据
   */
  static mergeNodeData(target: FlowNode, source: Partial<FlowNode>): FlowNode {
    return {
      ...target,
      ...source,
      data: {
        ...target.data,
        ...source.data
      }
    };
  }

  /**
   * 获取节点的变量引用
   */
  static getNodeVariableReferences(node: FlowNode): string[] {
    const references: string[] = [];
    
    // 从提示词中提取变量引用
    if (node.data.prompt) {
      const matches = node.data.prompt.match(/\{\{([^}]+)\}\}/g);
      if (matches) {
        matches.forEach(match => {
          const variable = match.replace(/\{\{|\}\}/g, '');
          references.push(variable);
        });
      }
    }

    // 从其他字段中提取变量引用
    const checkForVariables = (obj: any) => {
      if (typeof obj === 'string') {
        const matches = obj.match(/\{\{([^}]+)\}\}/g);
        if (matches) {
          matches.forEach(match => {
            const variable = match.replace(/\{\{|\}\}/g, '');
            references.push(variable);
          });
        }
      } else if (typeof obj === 'object' && obj !== null) {
        Object.values(obj).forEach(checkForVariables);
      }
    };

    checkForVariables(node.data.options);

    return [...new Set(references)]; // 去重
  }
}

export type { FlowField, FlowData, FlowNode, FlowEdge };
