/**
 * AI Flow 编排系统 - 原生 React Flow 版本
 * 使用原生 React Flow 替代 @xrenders/xflow 以解决兼容性问题
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  ReactFlow,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  Panel,
  ReactFlowProvider,
  type Node,
  type Edge,
  type Connection,
} from '@xyflow/react';
import { Button, Space, message, Drawer, Tooltip } from 'antd';
import {
  SaveOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  UndoOutlined,
  RedoOutlined,
  DownloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';

// 导入 React Flow 样式
import '@xyflow/react/dist/style.css';

// 节点类型定义
const nodeTypes = {
  start: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#409eff',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #409eff',
      boxShadow: '0 2px 8px rgba(64, 158, 255, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🚀 开始</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.name || '开始节点'}
      </div>
    </div>
  ),

  llm: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#6172F3',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #6172F3',
      boxShadow: '0 2px 8px rgba(97, 114, 243, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🤖 LLM</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.model || 'gpt-3.5-turbo'}
      </div>
    </div>
  ),

  condition: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#f56c6c',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #f56c6c',
      boxShadow: '0 2px 8px rgba(245, 108, 108, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🔀 条件判断</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.field || '判断条件'}
      </div>
    </div>
  ),

  code: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#67c23a',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #67c23a',
      boxShadow: '0 2px 8px rgba(103, 194, 58, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>💻 代码执行</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.language || 'JavaScript'}
      </div>
    </div>
  ),

  http: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#909399',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #909399',
      boxShadow: '0 2px 8px rgba(144, 147, 153, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🌐 HTTP请求</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.method || 'GET'}
      </div>
    </div>
  ),

  knowledge: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#e6a23c',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #e6a23c',
      boxShadow: '0 2px 8px rgba(230, 162, 60, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>📚 知识库</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.knowledgeBase || '默认知识库'}
      </div>
    </div>
  ),

  end: ({ data }: { data: any }) => (
    <div style={{
      padding: '12px',
      background: '#f56c6c',
      color: 'white',
      borderRadius: '8px',
      minWidth: '120px',
      textAlign: 'center',
      border: '2px solid #f56c6c',
      boxShadow: '0 2px 8px rgba(245, 108, 108, 0.3)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>🏁 结束</div>
      <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
        {data.name || '结束节点'}
      </div>
    </div>
  ),
};

// 初始节点和边
const initialNodes: Node[] = [
  {
    id: 'start-1',
    type: 'start',
    position: { x: 100, y: 200 },
    data: { name: '开始' },
  },
  {
    id: 'llm-1',
    type: 'llm',
    position: { x: 400, y: 200 },
    data: { model: 'gpt-3.5-turbo', prompt: '你是一个智能助手' },
  },
  {
    id: 'end-1',
    type: 'end',
    position: { x: 700, y: 200 },
    data: { name: '结束' },
  },
];

const initialEdges: Edge[] = [
  { id: 'e1-2', source: 'start-1', target: 'llm-1' },
  { id: 'e2-3', source: 'llm-1', target: 'end-1' },
];

// 节点配置面板
const NodeConfigPanel = ({
  node,
  onClose,
  onUpdate
}: {
  node: Node | null;
  onClose: () => void;
  onUpdate: (nodeId: string, data: any) => void;
}) => {
  if (!node) return null;

  const handleUpdate = (field: string, value: any) => {
    onUpdate(node.id, { ...node.data, [field]: value });
  };

  const getNodeTitle = (type: string) => {
    const titles: { [key: string]: string } = {
      start: '🚀 开始节点',
      llm: '🤖 LLM节点',
      condition: '🔀 条件判断节点',
      code: '💻 代码执行节点',
      http: '🌐 HTTP请求节点',
      knowledge: '📚 知识库节点',
      end: '🏁 结束节点'
    };
    return titles[type] || `${type} 节点`;
  };

  return (
    <Drawer
      title={getNodeTitle(node.type)}
      placement="right"
      onClose={onClose}
      open={true}
      width={350}
    >
      <div style={{ padding: '16px' }}>
        {/* 开始节点配置 */}
        {node.type === 'start' && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>节点名称:</label>
              <input
                type="text"
                value={node.data.name || ''}
                onChange={(e) => handleUpdate('name', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
                placeholder="输入节点名称"
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>描述:</label>
              <textarea
                value={node.data.description || ''}
                onChange={(e) => handleUpdate('description', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px', height: '60px' }}
                placeholder="输入节点描述"
              />
            </div>
          </div>
        )}

        {/* LLM节点配置 */}
        {node.type === 'llm' && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>模型:</label>
              <select
                value={node.data.model || 'gpt-3.5-turbo'}
                onChange={(e) => handleUpdate('model', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              >
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="gpt-4">GPT-4</option>
                <option value="gpt-4-turbo">GPT-4 Turbo</option>
                <option value="claude-3">Claude-3</option>
              </select>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>温度 (0-2):</label>
              <input
                type="number"
                min="0"
                max="2"
                step="0.1"
                value={node.data.temperature || 0.7}
                onChange={(e) => handleUpdate('temperature', parseFloat(e.target.value))}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>最大令牌数:</label>
              <input
                type="number"
                min="1"
                max="4000"
                value={node.data.maxTokens || 1000}
                onChange={(e) => handleUpdate('maxTokens', parseInt(e.target.value))}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>提示词:</label>
              <textarea
                value={node.data.prompt || ''}
                onChange={(e) => handleUpdate('prompt', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px', height: '120px' }}
                placeholder="输入提示词模板，可以使用 {{变量名}} 引用变量"
              />
            </div>
          </div>
        )}

        {/* 条件判断节点配置 */}
        {node.type === 'condition' && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>判断字段:</label>
              <input
                type="text"
                value={node.data.field || ''}
                onChange={(e) => handleUpdate('field', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
                placeholder="输入要判断的字段名"
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>操作符:</label>
              <select
                value={node.data.operator || 'equal'}
                onChange={(e) => handleUpdate('operator', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              >
                <option value="equal">等于</option>
                <option value="notEqual">不等于</option>
                <option value="include">包含</option>
                <option value="exclude">不包含</option>
                <option value="greaterThan">大于</option>
                <option value="lessThan">小于</option>
                <option value="greaterEqual">大于等于</option>
                <option value="lessEqual">小于等于</option>
              </select>
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>比较值:</label>
              <input
                type="text"
                value={node.data.value || ''}
                onChange={(e) => handleUpdate('value', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
                placeholder="输入比较值"
              />
            </div>
          </div>
        )}

        {/* 代码执行节点配置 */}
        {node.type === 'code' && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>编程语言:</label>
              <select
                value={node.data.language || 'javascript'}
                onChange={(e) => handleUpdate('language', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              >
                <option value="javascript">JavaScript</option>
                <option value="python">Python</option>
                <option value="typescript">TypeScript</option>
              </select>
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>代码:</label>
              <textarea
                value={node.data.code || ''}
                onChange={(e) => handleUpdate('code', e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                  height: '200px',
                  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                  fontSize: '12px'
                }}
                placeholder="// 在这里编写代码&#10;function main(params) {&#10;  // 处理逻辑&#10;  return result;&#10;}"
              />
            </div>
          </div>
        )}

        {/* HTTP请求节点配置 */}
        {node.type === 'http' && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>请求方法:</label>
              <select
                value={node.data.method || 'GET'}
                onChange={(e) => handleUpdate('method', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
                <option value="PATCH">PATCH</option>
              </select>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>请求URL:</label>
              <input
                type="text"
                value={node.data.url || ''}
                onChange={(e) => handleUpdate('url', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
                placeholder="https://api.example.com/data"
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>请求头 (JSON):</label>
              <textarea
                value={node.data.headers || '{"Content-Type": "application/json"}'}
                onChange={(e) => handleUpdate('headers', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px', height: '80px' }}
                placeholder='{"Content-Type": "application/json"}'
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>请求体:</label>
              <textarea
                value={node.data.body || ''}
                onChange={(e) => handleUpdate('body', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px', height: '80px' }}
                placeholder='{"key": "value"}'
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>超时时间(秒):</label>
              <input
                type="number"
                min="1"
                max="300"
                value={node.data.timeout || 30}
                onChange={(e) => handleUpdate('timeout', parseInt(e.target.value))}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              />
            </div>
          </div>
        )}

        {/* 知识库节点配置 */}
        {node.type === 'knowledge' && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>知识库:</label>
              <select
                value={node.data.knowledgeBase || 'default'}
                onChange={(e) => handleUpdate('knowledgeBase', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              >
                <option value="default">默认知识库</option>
                <option value="custom">自定义知识库</option>
                <option value="faq">FAQ知识库</option>
                <option value="product">产品知识库</option>
              </select>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>查询内容:</label>
              <textarea
                value={node.data.query || ''}
                onChange={(e) => handleUpdate('query', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px', height: '80px' }}
                placeholder="输入查询内容，可以使用 {{变量名}} 引用变量"
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>返回条数:</label>
              <input
                type="number"
                min="1"
                max="20"
                value={node.data.topK || 5}
                onChange={(e) => handleUpdate('topK', parseInt(e.target.value))}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>相似度阈值:</label>
              <input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={node.data.similarity || 0.7}
                onChange={(e) => handleUpdate('similarity', parseFloat(e.target.value))}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              />
            </div>
          </div>
        )}

        {/* 结束节点配置 */}
        {node.type === 'end' && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>节点名称:</label>
              <input
                type="text"
                value={node.data.name || ''}
                onChange={(e) => handleUpdate('name', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
                placeholder="输入节点名称"
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>输出格式:</label>
              <select
                value={node.data.outputFormat || 'json'}
                onChange={(e) => handleUpdate('outputFormat', e.target.value)}
                style={{ width: '100%', padding: '8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              >
                <option value="json">JSON</option>
                <option value="text">纯文本</option>
                <option value="markdown">Markdown</option>
              </select>
            </div>
          </div>
        )}
      </div>
    </Drawer>
  );
};

function ReactFlowApp() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);

  // 历史记录管理
  const [history, setHistory] = useState<{ nodes: Node[], edges: Edge[] }[]>([{ nodes: initialNodes, edges: initialEdges }]);
  const [historyIndex, setHistoryIndex] = useState(0);

  // 保存历史记录
  const saveToHistory = useCallback((newNodes: Node[], newEdges: Edge[]) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push({ nodes: newNodes, edges: newEdges });
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [history, historyIndex]);

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdges = addEdge(params, edges);
      setEdges(newEdges);
      saveToHistory(nodes, newEdges);
    },
    [setEdges, edges, nodes, saveToHistory]
  );

  // 撤销功能
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const prevState = history[historyIndex - 1];
      setNodes(prevState.nodes);
      setEdges(prevState.edges);
      setHistoryIndex(historyIndex - 1);
    }
  }, [history, historyIndex, setNodes, setEdges]);

  // 重做功能
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const nextState = history[historyIndex + 1];
      setNodes(nextState.nodes);
      setEdges(nextState.edges);
      setHistoryIndex(historyIndex + 1);
    }
  }, [history, historyIndex, setNodes, setEdges]);

  const onNodeClick = useCallback((_: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
  }, []);

  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
  }, []);

  const updateNodeData = useCallback((nodeId: string, newData: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId ? { ...node, data: newData } : node
      )
    );
  }, [setNodes]);

  const addNode = useCallback((type: string) => {
    // 为不同节点类型设置默认数据
    const getDefaultData = (nodeType: string) => {
      switch (nodeType) {
        case 'start':
          return { name: '开始', description: '流程开始节点' };
        case 'llm':
          return {
            model: 'gpt-3.5-turbo',
            temperature: 0.7,
            maxTokens: 1000,
            prompt: '你是一个智能助手，请回答用户的问题。'
          };
        case 'condition':
          return { field: '', operator: 'equal', value: '' };
        case 'code':
          return {
            language: 'javascript',
            code: `// 在这里编写代码
function main(params) {
  // params 包含输入参数
  console.log('输入参数:', params);

  // 处理逻辑
  const result = {
    message: '处理完成',
    data: params
  };

  // 返回结果
  return result;
}`
          };
        case 'http':
          return {
            method: 'GET',
            url: '',
            headers: '{"Content-Type": "application/json"}',
            body: '',
            timeout: 30
          };
        case 'knowledge':
          return {
            knowledgeBase: 'default',
            query: '',
            topK: 5,
            similarity: 0.7
          };
        case 'end':
          return { name: '结束', outputFormat: 'json' };
        default:
          return { name: nodeType };
      }
    };

    const newNode: Node = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: Math.random() * 400 + 200, y: Math.random() * 400 + 200 },
      data: getDefaultData(type),
    };
    const newNodes = [...nodes, newNode];
    setNodes(newNodes);
    saveToHistory(newNodes, edges);
  }, [nodes, edges, setNodes, saveToHistory]);

  const handleSave = useCallback(() => {
    const flowData = { nodes, edges };
    console.log('保存流程数据:', flowData);
    message.success('流程已保存');
  }, [nodes, edges]);

  const handleRun = useCallback(() => {
    console.log('运行流程:', { nodes, edges });
    message.info('开始运行流程...');
  }, [nodes, edges]);

  // 快捷键支持
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            handleSave();
            break;
          case 'z':
            if (event.shiftKey) {
              event.preventDefault();
              redo();
            } else {
              event.preventDefault();
              undo();
            }
            break;
          case 'y':
            event.preventDefault();
            redo();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleSave, undo, redo]);

  return (
    <div style={{ height: '100vh', background: '#f0f2f5' }}>
      {/* 工具栏 */}
      <div style={{
        height: '60px',
        background: '#fff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 24px',
        borderBottom: '1px solid #e8e8e8',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ margin: 0, fontSize: '20px', fontWeight: 600 }}>
          🚀 AI Flow 编排系统 - React Flow 版本
        </h1>

        <Space size="middle">
          <Space.Compact>
            <Tooltip title="撤销 (Ctrl+Z)">
              <Button
                icon={<UndoOutlined />}
                disabled={historyIndex <= 0}
                onClick={undo}
              />
            </Tooltip>
            <Tooltip title="重做 (Ctrl+Y)">
              <Button
                icon={<RedoOutlined />}
                disabled={historyIndex >= history.length - 1}
                onClick={redo}
              />
            </Tooltip>
          </Space.Compact>

          <Tooltip title="保存流程 (Ctrl+S)">
            <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
              保存
            </Button>
          </Tooltip>

          <Tooltip title="运行流程">
            <Button icon={<PlayCircleOutlined />} onClick={handleRun}>
              运行
            </Button>
          </Tooltip>

          <Tooltip title="导出流程">
            <Button icon={<DownloadOutlined />} onClick={() => message.info('导出功能开发中')}>
              导出
            </Button>
          </Tooltip>

          <Tooltip title="导入流程">
            <Button icon={<UploadOutlined />} onClick={() => message.info('导入功能开发中')}>
              导入
            </Button>
          </Tooltip>
        </Space>
      </div>

      {/* 主要内容区域 */}
      <div style={{ height: 'calc(100vh - 60px)', position: 'relative' }}>
        <ReactFlowProvider>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onPaneClick={onPaneClick}
            nodeTypes={nodeTypes}
            fitView
          >
            <Controls />
            <MiniMap />
            <Background />
            
            {/* 节点添加面板 */}
            <Panel position="top-left">
              <div style={{
                background: 'white',
                padding: '16px',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                maxWidth: '200px'
              }}>
                <div style={{ marginBottom: '12px', fontWeight: 'bold', fontSize: '14px' }}>
                  🛠️ 节点工具箱
                </div>

                <div style={{ marginBottom: '12px' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>基础节点</div>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Button
                      size="small"
                      onClick={() => addNode('start')}
                      block
                      style={{ textAlign: 'left', height: '32px' }}
                    >
                      🚀 开始节点
                    </Button>
                    <Button
                      size="small"
                      onClick={() => addNode('end')}
                      block
                      style={{ textAlign: 'left', height: '32px' }}
                    >
                      🏁 结束节点
                    </Button>
                  </Space>
                </div>

                <div style={{ marginBottom: '12px' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>AI节点</div>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Button
                      size="small"
                      onClick={() => addNode('llm')}
                      block
                      style={{ textAlign: 'left', height: '32px' }}
                    >
                      🤖 LLM节点
                    </Button>
                    <Button
                      size="small"
                      onClick={() => addNode('knowledge')}
                      block
                      style={{ textAlign: 'left', height: '32px' }}
                    >
                      📚 知识库
                    </Button>
                  </Space>
                </div>

                <div style={{ marginBottom: '12px' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>逻辑节点</div>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Button
                      size="small"
                      onClick={() => addNode('condition')}
                      block
                      style={{ textAlign: 'left', height: '32px' }}
                    >
                      🔀 条件判断
                    </Button>
                  </Space>
                </div>

                <div>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>行为节点</div>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Button
                      size="small"
                      onClick={() => addNode('code')}
                      block
                      style={{ textAlign: 'left', height: '32px' }}
                    >
                      💻 代码执行
                    </Button>
                    <Button
                      size="small"
                      onClick={() => addNode('http')}
                      block
                      style={{ textAlign: 'left', height: '32px' }}
                    >
                      🌐 HTTP请求
                    </Button>
                  </Space>
                </div>
              </div>
            </Panel>
          </ReactFlow>
        </ReactFlowProvider>

        {/* 节点配置面板 */}
        <NodeConfigPanel
          node={selectedNode}
          onClose={() => setSelectedNode(null)}
          onUpdate={updateNodeData}
        />
      </div>
    </div>
  );
}

export default ReactFlowApp;
