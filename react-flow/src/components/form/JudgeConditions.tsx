import React, { useState } from 'react';
import { Button, Input, Select, Form, Space, Card, Typography, Radio } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

interface JudgeCondition {
  field: string;
  nodeId?: string;
  nodeType?: string;
  condition: string;
  value?: string;
  operator: 'OR' | 'AND';
}

interface JudgeConditionsProps {
  value?: JudgeCondition[];
  onChange?: (value: JudgeCondition[]) => void;
}

const JudgeConditions: React.FC<JudgeConditionsProps> = ({
  value = [],
  onChange
}) => {
  const [conditions, setConditions] = useState<JudgeCondition[]>(value);

  const handleChange = (newConditions: JudgeCondition[]) => {
    setConditions(newConditions);
    onChange?.(newConditions);
  };

  const addCondition = () => {
    const newCondition: JudgeCondition = {
      field: '',
      condition: 'equal',
      value: '',
      operator: 'OR'
    };
    handleChange([...conditions, newCondition]);
  };

  const removeCondition = (index: number) => {
    const newConditions = conditions.filter((_, i) => i !== index);
    handleChange(newConditions);
  };

  const updateCondition = (index: number, updates: Partial<JudgeCondition>) => {
    const newConditions = conditions.map((condition, i) => 
      i === index ? { ...condition, ...updates } : condition
    );
    handleChange(newConditions);
  };

  const conditionOptions = [
    { label: '包含', value: 'include' },
    { label: '不包含', value: 'exclude' },
    { label: '开始是', value: 'startWith' },
    { label: '结束是', value: 'endWith' },
    { label: '等于', value: 'equal' },
    { label: '不等于', value: 'notEqual' },
    { label: '大于', value: 'greaterThan' },
    { label: '大于等于', value: 'greaterThanOrEqual' },
    { label: '小于', value: 'lessThan' },
    { label: '小于等于', value: 'lessThanOrEqual' },
    { label: '为空', value: 'isNull' },
    { label: '不为空', value: 'isNotNull' }
  ];

  const needsValue = (condition: string) => {
    return !['isNull', 'isNotNull'].includes(condition);
  };

  return (
    <div>
      {conditions.map((condition, index) => (
        <Card
          key={index}
          size="small"
          style={{ marginBottom: 8 }}
          bodyStyle={{ padding: '12px' }}
        >
          <Space direction="vertical" style={{ width: '100%' }} size="small">
            {/* 第一行：变量选择 */}
            <Space style={{ width: '100%' }}>
              <Text strong style={{ width: 60 }}>变量:</Text>
              <Input
                value={condition.field}
                onChange={(e) => updateCondition(index, { field: e.target.value })}
                placeholder="选择变量"
                style={{ flex: 1 }}
              />
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => removeCondition(index)}
              />
            </Space>

            {/* 第二行：条件和值 */}
            <Space style={{ width: '100%' }}>
              <Text strong style={{ width: 60 }}>条件:</Text>
              <Select
                value={condition.condition}
                onChange={(value) => updateCondition(index, { condition: value })}
                style={{ width: 120 }}
                size="small"
              >
                {conditionOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>

              {needsValue(condition.condition) && (
                <>
                  <Text>值:</Text>
                  <Input
                    value={condition.value}
                    onChange={(e) => updateCondition(index, { value: e.target.value })}
                    placeholder="输入比较值"
                    style={{ flex: 1 }}
                  />
                </>
              )}
            </Space>

            {/* 第三行：逻辑关系（除了最后一个条件） */}
            {index < conditions.length - 1 && (
              <Space style={{ width: '100%' }}>
                <Text strong style={{ width: 60 }}>逻辑:</Text>
                <Radio.Group
                  value={condition.operator}
                  onChange={(e) => updateCondition(index, { operator: e.target.value })}
                  size="small"
                >
                  <Radio.Button value="OR">或 (OR)</Radio.Button>
                  <Radio.Button value="AND">且 (AND)</Radio.Button>
                </Radio.Group>
              </Space>
            )}

            {/* 变量绑定信息 */}
            {condition.nodeId && (
              <Space>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  绑定到: {condition.nodeType}#{condition.nodeId}
                </Text>
              </Space>
            )}
          </Space>
        </Card>
      ))}

      <Button
        type="dashed"
        onClick={addCondition}
        icon={<PlusOutlined />}
        style={{ width: '100%' }}
      >
        添加判断条件
      </Button>

      {conditions.length > 0 && (
        <div style={{ marginTop: 8, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <strong>逻辑说明:</strong> 多个条件之间按照设置的逻辑关系（OR/AND）进行组合判断。
            OR 表示任一条件满足即可，AND 表示所有条件都必须满足。
          </Text>
        </div>
      )}
    </div>
  );
};

export default JudgeConditions;
