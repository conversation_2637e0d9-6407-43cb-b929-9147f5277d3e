import React, { useState, useRef } from 'react';
import { Input, Button, Space, Typography, Card, Tabs } from 'antd';
import { FullscreenOutlined, CompressOutlined, PlayCircleOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Text } = Typography;
const { TabPane } = Tabs;

interface CodeEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  language?: string;
  height?: number;
  placeholder?: string;
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  value = '',
  onChange,
  language = 'javascript',
  height = 300,
  placeholder = '请输入代码...'
}) => {
  const [code, setCode] = useState(value);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [activeTab, setActiveTab] = useState('editor');
  const textAreaRef = useRef<any>(null);

  const handleChange = (newCode: string) => {
    setCode(newCode);
    onChange?.(newCode);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const formatCode = () => {
    // 简单的代码格式化
    try {
      const formatted = code
        .split('\n')
        .map(line => line.trim())
        .join('\n')
        .replace(/\n\n+/g, '\n\n'); // 移除多余空行
      handleChange(formatted);
    } catch (error) {
      console.error('代码格式化失败:', error);
    }
  };

  const testCode = () => {
    // 简单的代码测试
    try {
      // 这里可以添加代码测试逻辑
      console.log('测试代码:', code);
      // 可以使用 eval 或其他方式测试代码
    } catch (error) {
      console.error('代码测试失败:', error);
    }
  };

  const editorStyle = {
    height: isFullscreen ? 'calc(100vh - 200px)' : height,
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
    fontSize: '14px',
    lineHeight: '1.5'
  };

  const containerStyle = isFullscreen ? {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    background: '#fff',
    padding: '20px'
  } : {};

  const codeTemplates = {
    javascript: `import axios from 'axios';
import * as _ from 'lodash';
import * as moment from 'moment';

/**
 * 执行代码
 * @param params 输入参数
 * @param ctx 上下文
 */
export default async function (params, ctx) {
  // 在这里编写你的代码
  console.log('输入参数:', params);
  
  // 示例：处理数据
  const result = {
    message: 'Hello World',
    timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
    input: params
  };
  
  // 返回结果
  return result;
}`,
    python: `import json
import datetime

def main(params, ctx):
    """
    执行代码
    :param params: 输入参数
    :param ctx: 上下文
    :return: 处理结果
    """
    print(f"输入参数: {params}")
    
    # 在这里编写你的代码
    result = {
        "message": "Hello World",
        "timestamp": datetime.datetime.now().isoformat(),
        "input": params
    }
    
    return result`
  };

  return (
    <div style={containerStyle}>
      <Card
        title={
          <Space>
            <Text strong>代码编辑器</Text>
            <Text type="secondary">({language})</Text>
          </Space>
        }
        extra={
          <Space>
            <Button size="small" onClick={formatCode}>
              格式化
            </Button>
            <Button size="small" icon={<PlayCircleOutlined />} onClick={testCode}>
              测试
            </Button>
            <Button
              size="small"
              icon={isFullscreen ? <CompressOutlined /> : <FullscreenOutlined />}
              onClick={toggleFullscreen}
            >
              {isFullscreen ? '退出全屏' : '全屏'}
            </Button>
          </Space>
        }
        bodyStyle={{ padding: 0 }}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="代码编辑" key="editor">
            <div style={{ padding: '12px' }}>
              <TextArea
                ref={textAreaRef}
                value={code}
                onChange={(e) => handleChange(e.target.value)}
                placeholder={placeholder}
                style={editorStyle}
                autoSize={false}
              />
            </div>
          </TabPane>
          
          <TabPane tab="代码模板" key="template">
            <div style={{ padding: '12px' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text type="secondary">选择一个模板开始编写代码：</Text>
                
                <Button
                  type="dashed"
                  onClick={() => {
                    handleChange(codeTemplates.javascript);
                    setActiveTab('editor');
                  }}
                  style={{ width: '100%', textAlign: 'left' }}
                >
                  JavaScript 模板
                </Button>
                
                <Button
                  type="dashed"
                  onClick={() => {
                    handleChange(codeTemplates.python);
                    setActiveTab('editor');
                  }}
                  style={{ width: '100%', textAlign: 'left' }}
                >
                  Python 模板
                </Button>
              </Space>
            </div>
          </TabPane>
          
          <TabPane tab="帮助文档" key="help">
            <div style={{ padding: '12px' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text strong>代码编写指南：</Text>
                
                <div>
                  <Text strong>1. 函数签名：</Text>
                  <pre style={{ background: '#f5f5f5', padding: '8px', margin: '4px 0' }}>
{`// JavaScript
export default async function (params, ctx) {
  // 你的代码
  return result;
}

# Python  
def main(params, ctx):
    # 你的代码
    return result`}
                  </pre>
                </div>
                
                <div>
                  <Text strong>2. 参数说明：</Text>
                  <ul>
                    <li><code>params</code>: 输入参数对象</li>
                    <li><code>ctx</code>: 上下文对象，包含流程信息</li>
                  </ul>
                </div>
                
                <div>
                  <Text strong>3. 可用库：</Text>
                  <ul>
                    <li>axios - HTTP 请求库</li>
                    <li>lodash - 工具函数库</li>
                    <li>moment - 时间处理库</li>
                  </ul>
                </div>
                
                <div>
                  <Text strong>4. 返回值：</Text>
                  <Text type="secondary">函数必须返回一个对象，对象的属性将作为输出变量。</Text>
                </div>
              </Space>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default CodeEditor;
