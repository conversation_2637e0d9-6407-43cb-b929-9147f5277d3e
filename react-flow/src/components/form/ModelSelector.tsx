import React, { useState } from 'react';
import { Select, Input, InputNumber, Form, Space, Card, Typography, Collapse, Button } from 'antd';
import { SettingOutlined } from '@ant-design/icons';

const { Option } = Select;
const { Text } = Typography;
const { Panel } = Collapse;

interface ModelConfig {
  configId: string;
  params: {
    model: string;
    temperature: number;
    max_tokens: number;
    top_p: number;
    frequency_penalty: number;
    presence_penalty: number;
  };
}

interface ModelSelectorProps {
  value?: ModelConfig;
  onChange?: (value: ModelConfig) => void;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  value,
  onChange
}) => {
  const [config, setConfig] = useState<ModelConfig>(value || {
    configId: '',
    params: {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      max_tokens: 1000,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0
    }
  });

  const handleChange = (updates: Partial<ModelConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    onChange?.(newConfig);
  };

  const handleParamsChange = (paramUpdates: Partial<ModelConfig['params']>) => {
    const newParams = { ...config.params, ...paramUpdates };
    handleChange({ params: newParams });
  };

  const modelOptions = [
    { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo', provider: 'OpenAI' },
    { label: 'GPT-4', value: 'gpt-4', provider: 'OpenAI' },
    { label: 'GPT-4 Turbo', value: 'gpt-4-turbo', provider: 'OpenAI' },
    { label: 'Claude-3 Haiku', value: 'claude-3-haiku', provider: 'Anthropic' },
    { label: 'Claude-3 Sonnet', value: 'claude-3-sonnet', provider: 'Anthropic' },
    { label: 'Claude-3 Opus', value: 'claude-3-opus', provider: 'Anthropic' },
  ];

  const presetConfigs = [
    {
      name: '平衡模式',
      description: '平衡创造性和准确性',
      params: { temperature: 0.7, top_p: 1, frequency_penalty: 0, presence_penalty: 0 }
    },
    {
      name: '创造模式',
      description: '更高的创造性和随机性',
      params: { temperature: 0.9, top_p: 0.9, frequency_penalty: 0.5, presence_penalty: 0.5 }
    },
    {
      name: '精确模式',
      description: '更准确和一致的输出',
      params: { temperature: 0.3, top_p: 0.8, frequency_penalty: 0, presence_penalty: 0 }
    },
    {
      name: '对话模式',
      description: '适合对话场景',
      params: { temperature: 0.8, top_p: 0.95, frequency_penalty: 0.3, presence_penalty: 0.3 }
    }
  ];

  const applyPreset = (preset: typeof presetConfigs[0]) => {
    handleParamsChange(preset.params);
  };

  return (
    <Card size="small" title="模型配置">
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {/* 配置ID */}
        <Form.Item label="配置ID" style={{ marginBottom: 8 }}>
          <Input
            value={config.configId}
            onChange={(e) => handleChange({ configId: e.target.value })}
            placeholder="可选：自定义配置ID"
          />
        </Form.Item>

        {/* 模型选择 */}
        <Form.Item label="模型" style={{ marginBottom: 8 }}>
          <Select
            value={config.params.model}
            onChange={(value) => handleParamsChange({ model: value })}
            style={{ width: '100%' }}
          >
            {modelOptions.map(option => (
              <Option key={option.value} value={option.value}>
                <Space>
                  <Text>{option.label}</Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    ({option.provider})
                  </Text>
                </Space>
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* 预设配置 */}
        <Form.Item label="预设配置" style={{ marginBottom: 8 }}>
          <Space wrap>
            {presetConfigs.map((preset, index) => (
              <Button
                key={index}
                size="small"
                onClick={() => applyPreset(preset)}
                title={preset.description}
              >
                {preset.name}
              </Button>
            ))}
          </Space>
        </Form.Item>

        {/* 高级参数 */}
        <Collapse size="small">
          <Panel header="高级参数" key="advanced">
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <Form.Item label="温度 (Temperature)" style={{ marginBottom: 8 }}>
                <InputNumber
                  value={config.params.temperature}
                  onChange={(value) => handleParamsChange({ temperature: value || 0 })}
                  min={0}
                  max={2}
                  step={0.1}
                  style={{ width: '100%' }}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  控制输出的随机性，0-2之间，值越高越随机
                </Text>
              </Form.Item>

              <Form.Item label="最大令牌数 (Max Tokens)" style={{ marginBottom: 8 }}>
                <InputNumber
                  value={config.params.max_tokens}
                  onChange={(value) => handleParamsChange({ max_tokens: value || 1000 })}
                  min={1}
                  max={4000}
                  style={{ width: '100%' }}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  生成文本的最大长度
                </Text>
              </Form.Item>

              <Form.Item label="Top P" style={{ marginBottom: 8 }}>
                <InputNumber
                  value={config.params.top_p}
                  onChange={(value) => handleParamsChange({ top_p: value || 1 })}
                  min={0}
                  max={1}
                  step={0.1}
                  style={{ width: '100%' }}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  核采样参数，控制词汇选择范围
                </Text>
              </Form.Item>

              <Form.Item label="频率惩罚 (Frequency Penalty)" style={{ marginBottom: 8 }}>
                <InputNumber
                  value={config.params.frequency_penalty}
                  onChange={(value) => handleParamsChange({ frequency_penalty: value || 0 })}
                  min={-2}
                  max={2}
                  step={0.1}
                  style={{ width: '100%' }}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  减少重复词汇的出现频率
                </Text>
              </Form.Item>

              <Form.Item label="存在惩罚 (Presence Penalty)" style={{ marginBottom: 0 }}>
                <InputNumber
                  value={config.params.presence_penalty}
                  onChange={(value) => handleParamsChange({ presence_penalty: value || 0 })}
                  min={-2}
                  max={2}
                  step={0.1}
                  style={{ width: '100%' }}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  鼓励模型谈论新话题
                </Text>
              </Form.Item>
            </Space>
          </Panel>
        </Collapse>

        {/* 当前配置预览 */}
        <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <strong>当前配置:</strong> {config.params.model} | 
            温度: {config.params.temperature} | 
            令牌: {config.params.max_tokens} | 
            Top P: {config.params.top_p}
          </Text>
        </div>
      </Space>
    </Card>
  );
};

export default ModelSelector;
