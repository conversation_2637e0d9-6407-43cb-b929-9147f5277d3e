import React, { useState } from 'react';
import { Button, Input, Select, Form, Space, Card, Typography, Popover } from 'antd';
import { PlusOutlined, DeleteOutlined, LinkOutlined } from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

interface Variable {
  field: string;
  nodeId?: string;
  nodeType?: string;
  name?: string;
  type?: string;
  label?: string;
  required?: boolean;
}

interface VariableListProps {
  value?: Variable[];
  onChange?: (value: Variable[]) => void;
  mode?: 'input' | 'output' | 'binding';
  allowTypes?: boolean;
  allowRequired?: boolean;
  allowBinding?: boolean;
  placeholder?: string;
}

const VariableList: React.FC<VariableListProps> = ({
  value = [],
  onChange,
  mode = 'input',
  allowTypes = true,
  allowRequired = false,
  allowBinding = true,
  placeholder = '请输入字段名'
}) => {
  const [variables, setVariables] = useState<Variable[]>(value);

  const handleChange = (newVariables: Variable[]) => {
    setVariables(newVariables);
    onChange?.(newVariables);
  };

  const addVariable = () => {
    const newVariable: Variable = {
      field: '',
      type: mode === 'output' ? 'string' : undefined
    };
    handleChange([...variables, newVariable]);
  };

  const removeVariable = (index: number) => {
    const newVariables = variables.filter((_, i) => i !== index);
    handleChange(newVariables);
  };

  const updateVariable = (index: number, updates: Partial<Variable>) => {
    const newVariables = variables.map((variable, i) => 
      i === index ? { ...variable, ...updates } : variable
    );
    handleChange(newVariables);
  };

  const typeOptions = [
    { label: '文本', value: 'string' },
    { label: '数字', value: 'number' },
    { label: '布尔值', value: 'boolean' },
    { label: '对象', value: 'object' },
    { label: '数组', value: 'array' }
  ];

  const renderVariableBinding = (variable: Variable, index: number) => {
    if (!allowBinding || mode === 'input') return null;

    return (
      <Popover
        content={
          <div style={{ width: 200 }}>
            <Text type="secondary">绑定到其他节点的输出变量</Text>
            <Form.Item label="节点ID" style={{ marginTop: 8, marginBottom: 8 }}>
              <Input
                value={variable.nodeId}
                onChange={(e) => updateVariable(index, { nodeId: e.target.value })}
                placeholder="输入节点ID"
                size="small"
              />
            </Form.Item>
            <Form.Item label="节点类型" style={{ marginBottom: 0 }}>
              <Input
                value={variable.nodeType}
                onChange={(e) => updateVariable(index, { nodeType: e.target.value })}
                placeholder="输入节点类型"
                size="small"
              />
            </Form.Item>
          </div>
        }
        title="变量绑定"
        trigger="click"
      >
        <Button
          size="small"
          icon={<LinkOutlined />}
          type={variable.nodeId ? 'primary' : 'default'}
        >
          {variable.nodeId ? '已绑定' : '绑定'}
        </Button>
      </Popover>
    );
  };

  return (
    <div>
      {variables.map((variable, index) => (
        <Card
          key={index}
          size="small"
          style={{ marginBottom: 8 }}
          bodyStyle={{ padding: '12px' }}
        >
          <Space direction="vertical" style={{ width: '100%' }} size="small">
            <Space style={{ width: '100%' }}>
              <Input
                value={variable.field}
                onChange={(e) => updateVariable(index, { field: e.target.value })}
                placeholder={placeholder}
                style={{ flex: 1 }}
              />
              
              {mode === 'input' && (
                <Input
                  value={variable.label}
                  onChange={(e) => updateVariable(index, { label: e.target.value })}
                  placeholder="显示名称"
                  style={{ width: 120 }}
                />
              )}

              {allowTypes && (
                <Select
                  value={variable.type}
                  onChange={(value) => updateVariable(index, { type: value })}
                  placeholder="类型"
                  style={{ width: 100 }}
                  size="small"
                >
                  {typeOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              )}

              {allowRequired && (
                <Select
                  value={variable.required}
                  onChange={(value) => updateVariable(index, { required: value })}
                  placeholder="必填"
                  style={{ width: 80 }}
                  size="small"
                >
                  <Option value={true}>必填</Option>
                  <Option value={false}>可选</Option>
                </Select>
              )}

              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => removeVariable(index)}
              />
            </Space>

            <Space>
              {renderVariableBinding(variable, index)}
              
              {variable.nodeId && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  绑定到: {variable.nodeType}#{variable.nodeId}
                </Text>
              )}
            </Space>
          </Space>
        </Card>
      ))}

      <Button
        type="dashed"
        onClick={addVariable}
        icon={<PlusOutlined />}
        style={{ width: '100%' }}
      >
        添加{mode === 'input' ? '输入' : '输出'}变量
      </Button>
    </div>
  );
};

export default VariableList;
