import React from 'react';
import { Input, InputNumber, Select, Switch, Form } from 'antd';
import VariableList from './VariableList';
import JudgeConditions from './JudgeConditions';
import CodeEditor from './CodeEditor';
import ModelSelector from './ModelSelector';

const { TextArea } = Input;
const { Option } = Select;

interface FormProperty {
  title: string;
  type: string;
  widget?: string;
  enum?: string[];
  enumNames?: string[];
  default?: any;
  minimum?: number;
  maximum?: number;
  step?: number;
  placeholder?: string;
  description?: string;
  properties?: { [key: string]: FormProperty };
  items?: FormProperty;
}

interface CustomFormRendererProps {
  schema: { [key: string]: FormProperty };
  value: any;
  onChange: (value: any) => void;
}

const CustomFormRenderer: React.FC<CustomFormRendererProps> = ({
  schema,
  value = {},
  onChange
}) => {
  const handleFieldChange = (field: string, fieldValue: any) => {
    const newValue = { ...value, [field]: fieldValue };
    onChange(newValue);
  };

  const renderField = (key: string, property: FormProperty, currentValue: any) => {
    const { type, widget, title, description, placeholder } = property;

    // 处理自定义 widget
    switch (widget) {
      case 'variableList':
      case 'fieldList':
        return (
          <VariableList
            value={currentValue}
            onChange={(val) => handleFieldChange(key, val)}
            mode={widget === 'fieldList' ? 'input' : 'binding'}
            allowTypes={true}
            allowRequired={widget === 'fieldList'}
            allowBinding={widget === 'variableList'}
            placeholder={placeholder}
          />
        );

      case 'judgeConditions':
        return (
          <JudgeConditions
            value={currentValue}
            onChange={(val) => handleFieldChange(key, val)}
          />
        );

      case 'codeEditor':
        return (
          <CodeEditor
            value={currentValue}
            onChange={(val) => handleFieldChange(key, val)}
            language={property.language || 'javascript'}
            placeholder={placeholder}
          />
        );

      case 'modelSelector':
        return (
          <ModelSelector
            value={currentValue}
            onChange={(val) => handleFieldChange(key, val)}
          />
        );

      case 'textarea':
        return (
          <TextArea
            value={currentValue}
            onChange={(e) => handleFieldChange(key, e.target.value)}
            placeholder={placeholder}
            rows={4}
          />
        );

      case 'keyValue':
        // 简单的键值对编辑器
        return (
          <TextArea
            value={typeof currentValue === 'object' ? JSON.stringify(currentValue, null, 2) : currentValue}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleFieldChange(key, parsed);
              } catch {
                handleFieldChange(key, e.target.value);
              }
            }}
            placeholder={placeholder || '{"key": "value"}'}
            rows={3}
          />
        );
    }

    // 处理基本类型
    switch (type) {
      case 'string':
        if (property.enum) {
          return (
            <Select
              value={currentValue}
              onChange={(val) => handleFieldChange(key, val)}
              placeholder={placeholder}
              style={{ width: '100%' }}
            >
              {property.enum.map((option, index) => (
                <Option key={option} value={option}>
                  {property.enumNames?.[index] || option}
                </Option>
              ))}
            </Select>
          );
        }
        return (
          <Input
            value={currentValue}
            onChange={(e) => handleFieldChange(key, e.target.value)}
            placeholder={placeholder}
          />
        );

      case 'number':
        return (
          <InputNumber
            value={currentValue}
            onChange={(val) => handleFieldChange(key, val)}
            min={property.minimum}
            max={property.maximum}
            step={property.step}
            placeholder={placeholder}
            style={{ width: '100%' }}
          />
        );

      case 'boolean':
        return (
          <Switch
            checked={currentValue}
            onChange={(checked) => handleFieldChange(key, checked)}
          />
        );

      case 'object':
        if (property.properties) {
          return (
            <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '12px' }}>
              <CustomFormRenderer
                schema={property.properties}
                value={currentValue || {}}
                onChange={(val) => handleFieldChange(key, val)}
              />
            </div>
          );
        }
        return (
          <TextArea
            value={typeof currentValue === 'object' ? JSON.stringify(currentValue, null, 2) : currentValue}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleFieldChange(key, parsed);
              } catch {
                handleFieldChange(key, e.target.value);
              }
            }}
            placeholder={placeholder || '{}'}
            rows={4}
          />
        );

      case 'array':
        if (property.items && widget === 'array') {
          // 简单数组处理
          const arrayValue = Array.isArray(currentValue) ? currentValue : [];
          return (
            <div>
              {arrayValue.map((item, index) => (
                <div key={index} style={{ marginBottom: 8 }}>
                  {renderField(`${key}[${index}]`, property.items!, item)}
                </div>
              ))}
            </div>
          );
        }
        return (
          <TextArea
            value={Array.isArray(currentValue) ? JSON.stringify(currentValue, null, 2) : currentValue}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleFieldChange(key, parsed);
              } catch {
                handleFieldChange(key, e.target.value);
              }
            }}
            placeholder={placeholder || '[]'}
            rows={3}
          />
        );

      default:
        return (
          <Input
            value={currentValue}
            onChange={(e) => handleFieldChange(key, e.target.value)}
            placeholder={placeholder}
          />
        );
    }
  };

  return (
    <div>
      {Object.entries(schema).map(([key, property]) => (
        <Form.Item
          key={key}
          label={property.title}
          help={property.description}
          style={{ marginBottom: 16 }}
        >
          {renderField(key, property, value[key] || property.default)}
        </Form.Item>
      ))}
    </div>
  );
};

export default CustomFormRenderer;
