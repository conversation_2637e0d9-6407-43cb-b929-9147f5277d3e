import React from 'react';
import { Input, InputNumber, Select, Switch, Form } from 'antd';

const { TextArea } = Input;
const { Option } = Select;

interface FormProperty {
  title: string;
  type: string;
  widget?: string;
  enum?: string[];
  enumNames?: string[];
  default?: any;
  minimum?: number;
  maximum?: number;
  step?: number;
  placeholder?: string;
  description?: string;
  properties?: { [key: string]: FormProperty };
  items?: FormProperty;
}

interface SimpleFormRendererProps {
  schema: { [key: string]: FormProperty };
  value: any;
  onChange: (value: any) => void;
}

const SimpleFormRenderer: React.FC<SimpleFormRendererProps> = ({
  schema,
  value = {},
  onChange
}) => {
  const handleFieldChange = (field: string, fieldValue: any) => {
    const newValue = { ...value, [field]: fieldValue };
    onChange(newValue);
  };

  const renderField = (key: string, property: FormProperty, currentValue: any) => {
    const { type, widget, title, description, placeholder } = property;

    // 处理 textarea widget
    if (widget === 'textarea') {
      return (
        <TextArea
          value={currentValue}
          onChange={(e) => handleFieldChange(key, e.target.value)}
          placeholder={placeholder}
          rows={4}
        />
      );
    }

    // 处理基本类型
    switch (type) {
      case 'string':
        if (property.enum) {
          return (
            <Select
              value={currentValue}
              onChange={(val) => handleFieldChange(key, val)}
              placeholder={placeholder}
              style={{ width: '100%' }}
            >
              {property.enum.map((option, index) => (
                <Option key={option} value={option}>
                  {property.enumNames?.[index] || option}
                </Option>
              ))}
            </Select>
          );
        }
        return (
          <Input
            value={currentValue}
            onChange={(e) => handleFieldChange(key, e.target.value)}
            placeholder={placeholder}
          />
        );

      case 'number':
        return (
          <InputNumber
            value={currentValue}
            onChange={(val) => handleFieldChange(key, val)}
            min={property.minimum}
            max={property.maximum}
            step={property.step}
            placeholder={placeholder}
            style={{ width: '100%' }}
          />
        );

      case 'boolean':
        return (
          <Switch
            checked={currentValue}
            onChange={(checked) => handleFieldChange(key, checked)}
          />
        );

      case 'object':
        if (property.properties) {
          return (
            <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '12px' }}>
              <SimpleFormRenderer
                schema={property.properties}
                value={currentValue || {}}
                onChange={(val) => handleFieldChange(key, val)}
              />
            </div>
          );
        }
        return (
          <TextArea
            value={typeof currentValue === 'object' ? JSON.stringify(currentValue, null, 2) : currentValue}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleFieldChange(key, parsed);
              } catch {
                handleFieldChange(key, e.target.value);
              }
            }}
            placeholder={placeholder || '{}'}
            rows={4}
          />
        );

      case 'array':
        // 简单数组处理 - 显示为 JSON
        return (
          <TextArea
            value={Array.isArray(currentValue) ? JSON.stringify(currentValue, null, 2) : currentValue}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleFieldChange(key, parsed);
              } catch {
                handleFieldChange(key, e.target.value);
              }
            }}
            placeholder={placeholder || '[]'}
            rows={3}
          />
        );

      default:
        return (
          <Input
            value={currentValue}
            onChange={(e) => handleFieldChange(key, e.target.value)}
            placeholder={placeholder}
          />
        );
    }
  };

  return (
    <div>
      {Object.entries(schema).map(([key, property]) => (
        <Form.Item
          key={key}
          label={property.title}
          help={property.description}
          style={{ marginBottom: 16 }}
        >
          {renderField(key, property, value[key] || property.default)}
        </Form.Item>
      ))}
    </div>
  );
};

export default SimpleFormRenderer;
