import React, { useState, useEffect } from 'react';
import { Menu, message } from 'antd';
import { 
  CopyOutlined, 
  DeleteOutlined, 
  PlayCircleOutlined,
  PlusOutlined,
  ClearOutlined,
  LayoutOutlined
} from '@ant-design/icons';

interface ContextMenuProps {
  visible: boolean;
  x: number;
  y: number;
  type: 'canvas' | 'node';
  nodeData?: any;
  onClose: () => void;
  onCopy?: () => void;
  onDelete?: () => void;
  onRun?: () => void;
  onAddNode?: () => void;
  onClear?: () => void;
  onArrange?: () => void;
}

const ContextMenu: React.FC<ContextMenuProps> = ({
  visible,
  x,
  y,
  type,
  nodeData,
  onClose,
  onCopy,
  onDelete,
  onRun,
  onAddNode,
  onClear,
  onArrange
}) => {
  useEffect(() => {
    const handleClickOutside = () => {
      onClose();
    };

    if (visible) {
      document.addEventListener('click', handleClickOutside);
      document.addEventListener('contextmenu', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('contextmenu', handleClickOutside);
    };
  }, [visible, onClose]);

  if (!visible) return null;

  const canvasMenuItems = [
    {
      key: 'add',
      label: '添加节点',
      icon: <PlusOutlined />,
      onClick: () => {
        onAddNode?.();
        onClose();
      }
    },
    {
      key: 'arrange',
      label: '整理节点',
      icon: <LayoutOutlined />,
      onClick: () => {
        onArrange?.();
        onClose();
        message.success('节点已整理');
      }
    },
    { type: 'divider' },
    {
      key: 'clear',
      label: '清空画布',
      icon: <ClearOutlined />,
      danger: true,
      onClick: () => {
        onClear?.();
        onClose();
        message.success('画布已清空');
      }
    }
  ];

  const nodeMenuItems = [
    {
      key: 'copy',
      label: '复制节点',
      icon: <CopyOutlined />,
      onClick: () => {
        onCopy?.();
        onClose();
        message.success('节点已复制');
      }
    },
    {
      key: 'run',
      label: '运行节点',
      icon: <PlayCircleOutlined />,
      disabled: !nodeData || ['start', 'end'].includes(nodeData?.type),
      onClick: () => {
        onRun?.();
        onClose();
        message.info('开始运行节点...');
      }
    },
    { type: 'divider' },
    {
      key: 'delete',
      label: '删除节点',
      icon: <DeleteOutlined />,
      danger: true,
      disabled: nodeData && ['start', 'end'].includes(nodeData?.type),
      onClick: () => {
        onDelete?.();
        onClose();
        message.success('节点已删除');
      }
    }
  ];

  const menuItems = type === 'canvas' ? canvasMenuItems : nodeMenuItems;

  return (
    <div
      style={{
        position: 'fixed',
        top: y,
        left: x,
        zIndex: 9999,
        background: '#fff',
        borderRadius: '6px',
        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.12)',
        border: '1px solid #d9d9d9',
        minWidth: '160px'
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <Menu
        mode="vertical"
        items={menuItems}
        style={{ border: 'none' }}
      />
    </div>
  );
};

export default ContextMenu;
