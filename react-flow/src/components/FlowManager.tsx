import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Table, 
  Button, 
  Space, 
  message, 
  Popconfirm, 
  Input, 
  Form,
  Typography,
  Tag,
  Upload
} from 'antd';
import { 
  DeleteOutlined, 
  DownloadOutlined, 
  UploadOutlined,
  EyeOutlined,
  EditOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { flowStorage, type SavedFlow, type FlowData } from '../services/flowStorage';

const { Text } = Typography;

interface FlowManagerProps {
  visible: boolean;
  onClose: () => void;
  onLoadFlow: (flowData: FlowData) => void;
  currentFlowData: FlowData;
}

const FlowManager: React.FC<FlowManagerProps> = ({
  visible,
  onClose,
  onLoadFlow,
  currentFlowData
}) => {
  const [flows, setFlows] = useState<SavedFlow[]>([]);
  const [loading, setLoading] = useState(false);
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      loadFlows();
    }
  }, [visible]);

  const loadFlows = () => {
    setLoading(true);
    try {
      const allFlows = flowStorage.getAllFlows();
      setFlows(allFlows);
    } catch (error) {
      message.error('加载流程列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveFlow = async (values: { name: string; description?: string }) => {
    try {
      const savedFlow = flowStorage.saveFlow(values.name, currentFlowData, values.description);
      message.success('流程保存成功');
      setSaveModalVisible(false);
      form.resetFields();
      loadFlows();
    } catch (error) {
      message.error('保存失败：' + error.message);
    }
  };

  const handleLoadFlow = (flow: SavedFlow) => {
    onLoadFlow(flow.data);
    message.success(`已加载流程：${flow.name}`);
    onClose();
  };

  const handleDeleteFlow = (id: string) => {
    try {
      flowStorage.deleteFlow(id);
      message.success('删除成功');
      loadFlows();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleExportFlow = (flow: SavedFlow) => {
    try {
      flowStorage.exportFlow(flow);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  const handleImportFlow = async (file: File) => {
    try {
      const importedFlow = await flowStorage.importFlow(file);
      message.success(`导入成功：${importedFlow.name}`);
      loadFlows();
    } catch (error) {
      message.error('导入失败：' + error.message);
    }
    return false; // 阻止默认上传行为
  };

  const handleExportAll = () => {
    try {
      flowStorage.exportAllFlows();
      message.success('批量导出成功');
    } catch (error) {
      message.error('批量导出失败');
    }
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '节点数',
      key: 'nodeCount',
      render: (record: SavedFlow) => (
        <Tag color="blue">{record.data.nodes.length} 个节点</Tag>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: SavedFlow) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleLoadFlow(record)}
          >
            加载
          </Button>
          <Button
            type="link"
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => handleExportFlow(record)}
          >
            导出
          </Button>
          <Popconfirm
            title="确定要删除这个流程吗？"
            onConfirm={() => handleDeleteFlow(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Modal
        title="流程管理"
        open={visible}
        onCancel={onClose}
        width={800}
        footer={[
          <Upload
            key="import"
            accept=".json"
            showUploadList={false}
            beforeUpload={handleImportFlow}
          >
            <Button icon={<UploadOutlined />}>导入流程</Button>
          </Upload>,
          <Button key="exportAll" onClick={handleExportAll}>
            批量导出
          </Button>,
          <Button 
            key="save" 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setSaveModalVisible(true)}
          >
            保存当前流程
          </Button>,
          <Button key="close" onClick={onClose}>
            关闭
          </Button>,
        ]}
      >
        <Table
          columns={columns}
          dataSource={flows}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: true,
          }}
          locale={{
            emptyText: '暂无保存的流程'
          }}
        />
        
        <div style={{ marginTop: 16, color: '#666', fontSize: '12px' }}>
          <Text type="secondary">
            存储信息：已使用 {Math.round(flowStorage.getStorageInfo().used / 1024)} KB
          </Text>
        </div>
      </Modal>

      <Modal
        title="保存流程"
        open={saveModalVisible}
        onCancel={() => {
          setSaveModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveFlow}
        >
          <Form.Item
            name="name"
            label="流程名称"
            rules={[{ required: true, message: '请输入流程名称' }]}
          >
            <Input placeholder="请输入流程名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="流程描述"
          >
            <Input.TextArea 
              placeholder="请输入流程描述（可选）" 
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default FlowManager;
