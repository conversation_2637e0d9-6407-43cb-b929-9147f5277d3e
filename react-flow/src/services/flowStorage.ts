interface FlowData {
  nodes: any[];
  edges: any[];
}

interface FlowMetadata {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  version: string;
}

interface SavedFlow extends FlowMetadata {
  data: FlowData;
}

class FlowStorageService {
  private readonly STORAGE_KEY = 'ai-flow-data';
  private readonly VERSION = '1.0.0';

  // 保存流程到本地存储
  saveFlow(name: string, data: FlowData, description?: string): SavedFlow {
    const flows = this.getAllFlows();
    const existingFlow = flows.find(f => f.name === name);
    
    const flowData: SavedFlow = {
      id: existingFlow?.id || this.generateId(),
      name,
      description,
      data,
      createdAt: existingFlow?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: this.VERSION
    };

    if (existingFlow) {
      // 更新现有流程
      const updatedFlows = flows.map(f => f.id === flowData.id ? flowData : f);
      this.saveToStorage(updatedFlows);
    } else {
      // 添加新流程
      flows.push(flowData);
      this.saveToStorage(flows);
    }

    return flowData;
  }

  // 加载流程
  loadFlow(id: string): SavedFlow | null {
    const flows = this.getAllFlows();
    return flows.find(f => f.id === id) || null;
  }

  // 获取所有流程
  getAllFlows(): SavedFlow[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to load flows from storage:', error);
      return [];
    }
  }

  // 删除流程
  deleteFlow(id: string): boolean {
    try {
      const flows = this.getAllFlows();
      const filteredFlows = flows.filter(f => f.id !== id);
      this.saveToStorage(filteredFlows);
      return true;
    } catch (error) {
      console.error('Failed to delete flow:', error);
      return false;
    }
  }

  // 导出流程为 JSON 文件
  exportFlow(flow: SavedFlow): void {
    const dataStr = JSON.stringify(flow, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${flow.name}-${flow.version}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  // 导入流程从 JSON 文件
  importFlow(file: File): Promise<SavedFlow> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string);
          
          // 验证数据格式
          if (!this.validateFlowData(data)) {
            throw new Error('Invalid flow data format');
          }
          
          // 生成新的 ID 避免冲突
          const importedFlow: SavedFlow = {
            ...data,
            id: this.generateId(),
            name: `${data.name} (导入)`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          
          // 保存到本地存储
          const flows = this.getAllFlows();
          flows.push(importedFlow);
          this.saveToStorage(flows);
          
          resolve(importedFlow);
        } catch (error) {
          reject(new Error('Failed to parse flow file: ' + error.message));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      
      reader.readAsText(file);
    });
  }

  // 批量导出所有流程
  exportAllFlows(): void {
    const flows = this.getAllFlows();
    const exportData = {
      version: this.VERSION,
      exportedAt: new Date().toISOString(),
      flows
    };
    
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `ai-flows-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  // 清空所有数据
  clearAllFlows(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  // 私有方法：保存到存储
  private saveToStorage(flows: SavedFlow[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(flows));
    } catch (error) {
      console.error('Failed to save flows to storage:', error);
      throw new Error('Storage quota exceeded or unavailable');
    }
  }

  // 私有方法：生成唯一 ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // 私有方法：验证流程数据格式
  private validateFlowData(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.name === 'string' &&
      data.data &&
      Array.isArray(data.data.nodes) &&
      Array.isArray(data.data.edges)
    );
  }

  // 获取存储使用情况
  getStorageInfo(): { used: number; total: number; percentage: number } {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY) || '';
      const used = new Blob([data]).size;
      const total = 5 * 1024 * 1024; // 假设 5MB 限制
      
      return {
        used,
        total,
        percentage: Math.round((used / total) * 100)
      };
    } catch (error) {
      return { used: 0, total: 0, percentage: 0 };
    }
  }
}

export const flowStorage = new FlowStorageService();
export type { FlowData, FlowMetadata, SavedFlow };
