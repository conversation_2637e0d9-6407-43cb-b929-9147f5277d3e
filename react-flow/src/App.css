/**
 * AI Flow 编排系统样式
 */

.app {
  height: 100vh;
  background-color: #f0f2f5;
}

.xflow-app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.current-view {
  color: #8c8c8c;
  font-size: 14px;
}

.header-right .ant-menu {
  border-bottom: none;
  background: transparent;
}

.xflow-app-sider {
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
}

.sider-content {
  padding: 16px 0;
  height: 100%;
  overflow-y: auto;
}

.sider-title {
  margin: 0 0 16px 0;
  padding: 0 16px;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.xflow-app-content {
  position: relative;
  overflow: hidden;
  background-color: #f0f2f5;
}

/* 编辑器容器 */
.editor-container {
  height: 100%;
  padding: 16px;
}

.editor-container .xflow-editor {
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 列表容器 */
.list-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

.list-container h2 {
  margin: 0 0 24px 0;
  font-size: 24px;
  font-weight: 500;
  color: #262626;
}

.flow-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

.flow-item {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.2s ease;
}

.flow-item:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.flow-item.selected {
  border-color: #1890ff;
  background-color: #f0f5ff;
}

.flow-item h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.flow-item p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .xflow-app-header {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
    height: auto;
  }
  
  .header-left {
    width: 100%;
    justify-content: center;
  }
  
  .header-right {
    width: 100%;
  }
  
  .header-right .ant-menu {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .app-title {
    font-size: 18px;
  }
  
  .current-view {
    font-size: 12px;
  }
  
  .xflow-app-sider {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }
  
  .list-container {
    padding: 16px;
  }
  
  .flow-list {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .flow-item {
    padding: 16px;
  }
  
  .editor-container {
    padding: 8px;
  }
}

/* 滚动条样式 */
.sider-content::-webkit-scrollbar,
.list-container::-webkit-scrollbar {
  width: 6px;
}

.sider-content::-webkit-scrollbar-track,
.list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sider-content::-webkit-scrollbar-thumb,
.list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sider-content::-webkit-scrollbar-thumb:hover,
.list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 菜单样式覆盖 */
.xflow-app .ant-menu-horizontal {
  line-height: 64px;
}

.xflow-app .ant-menu-horizontal > .ant-menu-item {
  border-bottom: 2px solid transparent;
}

.xflow-app .ant-menu-horizontal > .ant-menu-item-selected {
  border-bottom-color: #1890ff;
}

/* 侧边栏菜单样式 */
.xflow-app-sider .ant-menu-inline {
  border-right: none;
}

.xflow-app-sider .ant-menu-item {
  margin: 0;
  border-radius: 0;
}

.xflow-app-sider .ant-menu-item-selected {
  background-color: #e6f7ff;
  border-right: 3px solid #1890ff;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .xflow-app {
    background-color: #141414;
  }
  
  .xflow-app-header {
    background-color: #1f1f1f;
    border-bottom-color: #303030;
  }
  
  .app-title {
    color: #fff;
  }
  
  .current-view {
    color: #a6a6a6;
  }
  
  .xflow-app-sider {
    background-color: #1f1f1f;
    border-right-color: #303030;
  }
  
  .sider-title {
    color: #fff;
  }
  
  .xflow-app-content {
    background-color: #141414;
  }
  
  .editor-container .xflow-editor {
    background-color: #1f1f1f;
  }
  
  .list-container h2 {
    color: #fff;
  }
  
  .flow-item {
    background-color: #1f1f1f;
    border-color: #303030;
  }
  
  .flow-item:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
  }
  
  .flow-item.selected {
    background-color: #111b26;
  }
  
  .flow-item h4 {
    color: #fff;
  }
  
  .flow-item p {
    color: #a6a6a6;
  }
  
  .sider-content::-webkit-scrollbar-track,
  .list-container::-webkit-scrollbar-track {
    background: #303030;
  }
  
  .sider-content::-webkit-scrollbar-thumb,
  .list-container::-webkit-scrollbar-thumb {
    background: #595959;
  }
  
  .sider-content::-webkit-scrollbar-thumb:hover,
  .list-container::-webkit-scrollbar-thumb:hover {
    background: #737373;
  }
}
