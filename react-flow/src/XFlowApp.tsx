/**
 * AI Flow 编排系统 - @xrenders/xflow 版本
 * 基于 @xrenders/xflow 实现的流程编排界面
 */

import React, { useState, useCallback, useRef } from 'react';
import XFlow, { FlowProvider, useFlow } from '@xrenders/xflow';
import { Button, Space, Tooltip, Dropdown, Menu, message } from 'antd';
import {
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  DownloadOutlined,
  UploadOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import ContextMenu from './components/ContextMenu';
import FlowManager from './components/FlowManager';
import FlowValidatorComponent from './components/FlowValidator';
import VariableList from './components/form/VariableList';
import JudgeConditions from './components/form/JudgeConditions';
import CodeEditor from './components/form/CodeEditor';
import ModelSelector from './components/form/ModelSelector';
import CustomFormRenderer from './components/form/CustomFormRenderer';
import SimpleFormRenderer from './components/form/SimpleFormRenderer';
import { useKeyboard } from './hooks/useKeyboard';
import { useFlowHistory } from './hooks/useFlowHistory';
import { useSelection } from './hooks/useSelection';
import { flowStorage } from './services/flowStorage';
import { FlowValidator } from './utils/flowValidator';
import { NodeDataProcessor } from './utils/nodeDataProcessor';
import './App.css';

// 最简化的节点配置，避免复杂的配置导致问题
const nodeSettings = [
  {
    title: '开始',
    type: 'start',
    group: '基础',
    description: '开始节点',
    color: '#409eff',
    icon: { type: 'icon-start', bgColor: '#409eff' },
    settingSchema: {
      type: 'object',
      properties: {
        name: {
          title: '节点名称',
          type: 'string',
          default: '开始'
        }
      }
    }
  },
  {
    title: '结束',
    type: 'end',
    group: '基础',
    description: '结束节点',
    color: '#f56c6c',
    icon: { type: 'icon-end', bgColor: '#f56c6c' },
    settingSchema: {
      type: 'object',
      properties: {
        name: {
          title: '节点名称',
          type: 'string',
          default: '结束'
        }
      }
    }
  },
  {
    title: 'LLM',
    type: 'llm',
    group: '模型',
    description: '调用大语言模型',
    color: '#6172F3',
    icon: { type: 'icon-model', bgColor: '#6172F3' },
    settingSchema: {
      type: 'object',
      properties: {
        model: {
          title: '模型',
          type: 'string',
          enum: ['gpt-3.5-turbo', 'gpt-4'],
          default: 'gpt-3.5-turbo'
        },
        prompt: {
          title: '提示词',
          type: 'string',
          default: '你是一个智能助手'
        }
      }
    }
  },
  // 暂时移除其他节点，只保留基础的三个节点以确保稳定性
];

// 最简化的初始数据
const initialValues = {
  nodes: [
    {
      id: 'start',
      type: 'start',
      data: { name: '开始' },
      position: { x: 100, y: 200 }
    },
    {
      id: 'llm',
      type: 'llm',
      data: { 
        model: 'gpt-3.5-turbo',
        prompt: '你是一个智能助手'
      },
      position: { x: 400, y: 200 }
    },
    {
      id: 'end',
      type: 'end',
      data: { name: '结束' },
      position: { x: 700, y: 200 }
    }
  ],
  edges: [
    { id: 'start-llm', source: 'start', target: 'llm' },
    { id: 'llm-end', source: 'llm', target: 'end' }
  ]
};

// 工具栏组件
const Toolbar = ({
  onSave,
  onRun,
  onExport,
  onImport,
  onUndo,
  onRedo,
  onManage,
  onValidate,
  canUndo,
  canRedo,
  flowData
}: {
  onSave: () => void;
  onRun: () => void;
  onExport: () => void;
  onImport: () => void;
  onUndo: () => void;
  onRedo: () => void;
  onManage: () => void;
  onValidate: () => void;
  canUndo: boolean;
  canRedo: boolean;
  flowData: any;
}) => {
  // 实时验证状态
  const validationResult = FlowValidator.validate(flowData);
  const validationColor = validationResult.isValid
    ? (validationResult.warnings.length > 0 ? '#faad14' : '#52c41a')
    : '#ff4d4f';
  return (
    <div style={{
      height: '60px',
      background: '#fff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 24px',
      borderBottom: '1px solid #e8e8e8',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
    }}>
      <h1 style={{ margin: 0, fontSize: '20px', fontWeight: 600 }}>
        AI Flow 编排系统 - @xrenders/xflow 版本
      </h1>

      <Space size="middle">
        <Space.Compact>
          <Tooltip title="撤销 (Ctrl+Z)">
            <Button
              icon={<UndoOutlined />}
              disabled={!canUndo}
              onClick={onUndo}
            />
          </Tooltip>
          <Tooltip title="重做 (Ctrl+Y)">
            <Button
              icon={<RedoOutlined />}
              disabled={!canRedo}
              onClick={onRedo}
            />
          </Tooltip>
        </Space.Compact>

        <Tooltip title="保存流程 (Ctrl+S)">
          <Button type="primary" icon={<SaveOutlined />} onClick={onSave}>
            保存
          </Button>
        </Tooltip>

        <Tooltip title="验证流程">
          <Button
            icon={<CheckCircleOutlined />}
            onClick={onValidate}
            style={{ color: validationColor, borderColor: validationColor }}
          >
            验证
          </Button>
        </Tooltip>

        <Tooltip title="运行流程">
          <Button icon={<PlayCircleOutlined />} onClick={onRun}>
            运行
          </Button>
        </Tooltip>

        <Dropdown
          menu={{
            items: [
              {
                key: 'manage',
                label: '流程管理',
                icon: <SettingOutlined />,
                onClick: onManage
              },
              { type: 'divider' },
              {
                key: 'export',
                label: '导出流程',
                icon: <DownloadOutlined />,
                onClick: onExport
              },
              {
                key: 'import',
                label: '导入流程',
                icon: <UploadOutlined />,
                onClick: onImport
              }
            ]
          }}
          trigger={['click']}
        >
          <Button icon={<SettingOutlined />}>
            更多
          </Button>
        </Dropdown>
      </Space>
    </div>
  );
};

// 控制面板组件
const ControlPanel = () => {
  const [zoom, setZoom] = useState(100);

  const handleZoomIn = () => {
    const newZoom = Math.min(zoom + 25, 200);
    setZoom(newZoom);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom - 25, 25);
    setZoom(newZoom);
  };

  const handleFitView = () => {
    setZoom(100);
  };

  return (
    <div style={{
      position: 'absolute',
      bottom: '20px',
      right: '20px',
      background: '#fff',
      borderRadius: '8px',
      padding: '8px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      zIndex: 1000
    }}>
      <Space direction="vertical" size="small">
        <Tooltip title="放大">
          <Button size="small" icon={<ZoomInOutlined />} onClick={handleZoomIn} />
        </Tooltip>
        <div style={{ textAlign: 'center', fontSize: '12px', color: '#666' }}>
          {zoom}%
        </div>
        <Tooltip title="缩小">
          <Button size="small" icon={<ZoomOutOutlined />} onClick={handleZoomOut} />
        </Tooltip>
        <Tooltip title="适应画布">
          <Button size="small" icon={<FullscreenOutlined />} onClick={handleFitView} />
        </Tooltip>
      </Space>
    </div>
  );
};

function XFlowApp() {
  // 使用历史记录管理
  const {
    flowData,
    canUndo,
    canRedo,
    updateFlow,
    undo,
    redo
  } = useFlowHistory(initialValues);

  // 使用选择管理
  const {
    selectedNodes,
    selectedEdges,
    selectNode,
    selectEdge,
    selectAll,
    clearSelection,
    isNodeSelected,
    isEdgeSelected
  } = useSelection();

  const [contextMenu, setContextMenu] = useState({
    visible: false,
    x: 0,
    y: 0,
    type: 'canvas' as 'canvas' | 'node',
    nodeData: null as any
  });
  const [copiedNode, setCopiedNode] = useState<any>(null);
  const [flowManagerVisible, setFlowManagerVisible] = useState(false);
  const [validatorVisible, setValidatorVisible] = useState(false);

  const handleSave = useCallback(() => {
    try {
      // 使用数据处理器提取数据
      const extractedData = NodeDataProcessor.extractData(flowData.nodes, flowData.edges);

      const flowToSave = {
        id: Date.now().toString(),
        name: `流程_${new Date().toLocaleString()}`,
        description: '通过界面创建的流程',
        data: extractedData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      };

      flowStorage.saveFlow(flowToSave);
      message.success('流程已保存');
      console.log('保存流程数据:', extractedData);
    } catch (error) {
      message.error('保存失败：' + error.message);
      console.error('保存失败:', error);
    }
  }, [flowData]);

  const handleUndo = useCallback(() => {
    undo();
    message.info('已撤销');
  }, [undo]);

  const handleRedo = useCallback(() => {
    redo();
    message.info('已重做');
  }, [redo]);

  const handleRun = useCallback(() => {
    message.info('开始运行流程...');
    console.log('运行流程:', flowData);
  }, [flowData]);

  const handleCopy = useCallback(() => {
    if (selectedNodes.length > 0) {
      const selectedNode = flowData.nodes.find(node => node.id === selectedNodes[0]);
      if (selectedNode) {
        setCopiedNode({ ...selectedNode });
        message.success('节点已复制');
      }
    }
  }, [selectedNodes, flowData.nodes]);

  const handlePaste = useCallback(() => {
    if (copiedNode) {
      const newNode = {
        ...copiedNode,
        id: `${copiedNode.type}-${Date.now()}`,
        position: {
          x: copiedNode.position.x + 50,
          y: copiedNode.position.y + 50
        }
      };

      const newFlowData = {
        ...flowData,
        nodes: [...flowData.nodes, newNode]
      };

      updateFlow(newFlowData);
      message.success('节点已粘贴');
    }
  }, [copiedNode, flowData, updateFlow]);

  const handleDelete = useCallback(() => {
    if (selectedNodes.length > 0) {
      const nodesToDelete = selectedNodes.filter(nodeId => {
        const node = flowData.nodes.find(n => n.id === nodeId);
        return node && !['start', 'end'].includes(node.type);
      });

      if (nodesToDelete.length > 0) {
        const newFlowData = {
          nodes: flowData.nodes.filter(node => !nodesToDelete.includes(node.id)),
          edges: flowData.edges.filter(edge =>
            !nodesToDelete.includes(edge.source) && !nodesToDelete.includes(edge.target)
          )
        };

        updateFlow(newFlowData);
        clearSelection();
        message.success(`已删除 ${nodesToDelete.length} 个节点`);
      }
    }
  }, [selectedNodes, flowData, updateFlow, clearSelection]);

  const handleSelectAll = useCallback(() => {
    selectAll(flowData.nodes, flowData.edges);
    message.info('已全选');
  }, [selectAll, flowData]);

  const handleExport = useCallback(() => {
    try {
      // 使用数据处理器提取数据
      const extractedData = NodeDataProcessor.extractData(flowData.nodes, flowData.edges);

      // 创建临时的 SavedFlow 对象用于导出
      const tempFlow = {
        id: 'temp-export',
        name: '导出的流程',
        description: '通过快速导出功能导出的流程',
        data: extractedData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      };
      flowStorage.exportFlow(tempFlow);
      message.success('流程已导出');
    } catch (error) {
      message.error('导出失败：' + error.message);
    }
  }, [flowData]);

  const handleImport = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          const importedFlow = await flowStorage.importFlow(file);
          // 使用数据处理器还原数据
          const restoredData = NodeDataProcessor.restoreData(importedFlow.data);
          updateFlow(restoredData);
          message.success(`流程已导入：${importedFlow.name}`);
        } catch (error) {
          message.error('导入失败：' + error.message);
        }
      }
    };
    input.click();
  }, [updateFlow]);

  const handleManage = useCallback(() => {
    setFlowManagerVisible(true);
  }, []);

  const handleLoadFlow = useCallback((data: any) => {
    // 使用数据处理器还原数据
    const restoredData = NodeDataProcessor.restoreData(data);
    updateFlow(restoredData);
    clearSelection();
  }, [updateFlow, clearSelection]);

  const handleValidate = useCallback(() => {
    setValidatorVisible(true);
  }, []);

  const handleContextMenu = useCallback((event: React.MouseEvent, type: 'canvas' | 'node', nodeData?: any) => {
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      type,
      nodeData
    });
  }, []);

  const closeContextMenu = useCallback(() => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  }, []);

  // 注册快捷键
  useKeyboard({
    onSave: handleSave,
    onUndo: handleUndo,
    onRedo: handleRedo,
    onCopy: handleCopy,
    onPaste: handlePaste,
    onDelete: handleDelete,
    onSelectAll: handleSelectAll
  });

  return (
    <div style={{ height: '100vh', background: '#f0f2f5' }}>
      <Toolbar
        onSave={handleSave}
        onRun={handleRun}
        onExport={handleExport}
        onImport={handleImport}
        onUndo={handleUndo}
        onRedo={handleRedo}
        onManage={handleManage}
        onValidate={handleValidate}
        canUndo={canUndo}
        canRedo={canRedo}
        flowData={flowData}
      />

      <div
        style={{ height: 'calc(100vh - 60px)', position: 'relative' }}
        onContextMenu={(e) => handleContextMenu(e, 'canvas')}
      >
        <FlowProvider>
          <XFlow
            settings={nodeSettings}
            initialValues={flowData}
            iconFontUrl="//at.alicdn.com/t/a/font_4069358_caoh6qs1z9a.js"
            onChange={updateFlow}
          />
        </FlowProvider>

        <ControlPanel />

        <ContextMenu
          visible={contextMenu.visible}
          x={contextMenu.x}
          y={contextMenu.y}
          type={contextMenu.type}
          nodeData={contextMenu.nodeData}
          onClose={closeContextMenu}
          onCopy={handleCopy}
          onDelete={handleDelete}
          onRun={() => handleRun()}
          onAddNode={() => message.info('请从左侧工具栏拖拽节点')}
          onClear={() => {
            updateFlow({ nodes: [], edges: [] });
            clearSelection();
            message.success('画布已清空');
          }}
          onArrange={() => message.info('节点整理功能开发中')}
        />

        <FlowManager
          visible={flowManagerVisible}
          onClose={() => setFlowManagerVisible(false)}
          onLoadFlow={handleLoadFlow}
          currentFlowData={flowData}
        />

        <FlowValidatorComponent
          visible={validatorVisible}
          onClose={() => setValidatorVisible(false)}
          flowData={flowData}
        />
      </div>
    </div>
  );
}

export default XFlowApp;
