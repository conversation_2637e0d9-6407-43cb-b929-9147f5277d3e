/**
 * AI Flow 编排系统 - 版本选择器
 * 提供 @xrenders/xflow 和原生 React Flow 两个版本
 */

import React, { useState } from 'react';
import { Button, Space, Card, Alert } from 'antd';
import ReactFlowApp from './ReactFlowApp';
import FlowTestApp from './FlowTestApp';
// import XFlowApp from './XFlowApp'; // 暂时注释掉，专注于 React Flow 版本
import './App.css';

function App() {
  const [selectedVersion, setSelectedVersion] = useState<'reactflow' | 'xflow' | 'flowtest' | null>(null);

  if (selectedVersion === 'reactflow') {
    return <ReactFlowApp />;
  }

  if (selectedVersion === 'flowtest') {
    return <FlowTestApp />;
  }

  if (selectedVersion === 'xflow') {
    // return <XFlowApp />; // 暂时注释掉
    return <div>XFlow 版本开发中...</div>;
  }

  return (
    <div style={{
      height: '100vh',
      background: '#f0f2f5',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <div style={{ maxWidth: '800px', width: '100%' }}>
        <h1 style={{ textAlign: 'center', marginBottom: '40px', fontSize: '32px' }}>
          AI Flow 编排系统
        </h1>

        <Alert
          message="版本选择"
          description="我们提供两个版本供您选择。推荐使用 React Flow 版本，它更稳定且与 React 18 完全兼容。"
          type="info"
          style={{ marginBottom: '30px' }}
        />

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '20px' }}>
          <Card
            title="Flow Test App (最新)"
            extra={<span style={{ color: '#1890ff', fontWeight: 'bold' }}>转换版本</span>}
            style={{ cursor: 'pointer' }}
            hoverable
            onClick={() => setSelectedVersion('flowtest')}
          >
            <div style={{ marginBottom: '16px' }}>
              <h4>✅ 特性：</h4>
              <ul style={{ paddingLeft: '20px', margin: 0 }}>
                <li>完整的 Vue 转 React 版本</li>
                <li>保留所有原始功能</li>
                <li>React Flow + Antd</li>
                <li>国际化支持</li>
                <li>完整的节点系统</li>
              </ul>
            </div>
            <div>
              <h4>🎯 包含：</h4>
              <ul style={{ paddingLeft: '20px', margin: 0 }}>
                <li>流程设计器</li>
                <li>节点配置面板</li>
                <li>运行调试</li>
                <li>流程管理</li>
              </ul>
            </div>
          </Card>

          <Card
            title="React Flow 版本"
            extra={<span style={{ color: '#52c41a', fontWeight: 'bold' }}>稳定</span>}
            style={{ cursor: 'pointer' }}
            hoverable
            onClick={() => setSelectedVersion('reactflow')}
          >
            <div style={{ marginBottom: '16px' }}>
              <h4>✅ 优势：</h4>
              <ul style={{ paddingLeft: '20px', margin: 0 }}>
                <li>与 React 18 完全兼容</li>
                <li>性能优秀，无卡顿</li>
                <li>稳定可靠，无 Handle 错误</li>
                <li>原生 React Flow 生态</li>
                <li>更好的拖拽体验</li>
              </ul>
            </div>
            <div>
              <h4>⚠️ 限制：</h4>
              <ul style={{ paddingLeft: '20px', margin: 0 }}>
                <li>功能相对简单</li>
                <li>需要自定义更多组件</li>
              </ul>
            </div>
          </Card>

          <Card
            title="@xrenders/xflow 版本"
            extra={<span style={{ color: '#faad14', fontWeight: 'bold' }}>实验性</span>}
            style={{ cursor: 'pointer' }}
            hoverable
            onClick={() => setSelectedVersion('xflow')}
          >
            <div style={{ marginBottom: '16px' }}>
              <h4>✅ 优势：</h4>
              <ul style={{ paddingLeft: '20px', margin: 0 }}>
                <li>功能丰富</li>
                <li>内置表单渲染</li>
                <li>更多节点类型</li>
                <li>高级配置选项</li>
              </ul>
            </div>
            <div>
              <h4>⚠️ 已知问题：</h4>
              <ul style={{ paddingLeft: '20px', margin: 0 }}>
                <li>与 React 18 兼容性问题</li>
                <li>可能出现 Handle 错误</li>
                <li>拖拽时可能卡顿</li>
                <li>无限重渲染警告</li>
              </ul>
            </div>
          </Card>
        </div>

        <div style={{ textAlign: 'center', marginTop: '30px' }}>
          <Space size="large">
            <Button
              type="primary"
              size="large"
              onClick={() => setSelectedVersion('flowtest')}
            >
              使用 Flow Test App (推荐)
            </Button>
            <Button
              size="large"
              onClick={() => setSelectedVersion('reactflow')}
            >
              使用 React Flow 版本
            </Button>
            <Button
              size="large"
              onClick={() => setSelectedVersion('xflow')}
            >
              使用 @xrenders/xflow 版本
            </Button>
          </Space>
        </div>
      </div>
    </div>
  );
}



export default App;