import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, 
  Button, 
  Space, 
  Input, 
  Switch, 
  Popconfirm, 
  message,
  Card,
  Row,
  Col
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  FileTextOutlined,
  ReloadOutlined,
  SearchOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import FlowConfig from '../components/FlowConfig';
import dayjs from 'dayjs';

const { Search } = Input;

interface FlowItem {
  id: number;
  name: string;
  label: string;
  status: boolean;
  version: string;
  releaseTime?: string;
  description?: string;
  createTime: string;
  updateTime: string;
}

const FlowList: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<FlowItem[]>([]);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 加载数据
  const loadData = useCallback(async (params?: any) => {
    setLoading(true);
    try {
      // TODO: 实现实际的数据加载逻辑
      const mockData: FlowItem[] = [
        {
          id: 1,
          name: 'sample-flow-1',
          label: '示例流程1',
          status: true,
          version: '1.0.0',
          releaseTime: '2024-01-15 10:30:00',
          description: '这是一个示例流程',
          createTime: '2024-01-10 09:00:00',
          updateTime: '2024-01-15 10:30:00'
        },
        {
          id: 2,
          name: 'sample-flow-2',
          label: '示例流程2',
          status: false,
          version: '1.1.0',
          description: '另一个示例流程',
          createTime: '2024-01-12 14:20:00',
          updateTime: '2024-01-14 16:45:00'
        }
      ];

      setDataSource(mockData);
      setPagination(prev => ({
        ...prev,
        total: mockData.length
      }));
    } catch (error) {
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // 刷新
  const handleRefresh = useCallback(() => {
    loadData();
  }, [loadData]);

  // 新增
  const handleAdd = useCallback(() => {
    // TODO: 实现新增逻辑
    console.log('Add flow');
  }, []);

  // 编辑
  const handleEdit = useCallback((record: FlowItem) => {
    // TODO: 实现编辑逻辑
    console.log('Edit flow:', record);
  }, []);

  // 删除
  const handleDelete = useCallback(async (record: FlowItem) => {
    try {
      // TODO: 实现删除逻辑
      console.log('Delete flow:', record);
      message.success('删除成功');
      handleRefresh();
    } catch (error) {
      message.error('删除失败');
    }
  }, [handleRefresh]);

  // 编排
  const handleDesign = useCallback((record: FlowItem) => {
    navigate(`/flow/info?id=${record.id}`);
  }, [navigate]);

  // 查看日志
  const handleLogs = useCallback((record: FlowItem) => {
    // TODO: 实现查看日志逻辑
    console.log('View logs:', record);
  }, []);

  // 状态切换
  const handleStatusChange = useCallback(async (checked: boolean, record: FlowItem) => {
    try {
      // TODO: 实现状态切换逻辑
      console.log('Change status:', checked, record);
      message.success('状态更新成功');
      handleRefresh();
    } catch (error) {
      message.error('状态更新失败');
    }
  }, [handleRefresh]);

  // 搜索
  const handleSearch = useCallback((value: string) => {
    setSearchText(value);
    // TODO: 实现搜索逻辑
    loadData({ search: value });
  }, [loadData]);

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 140,
    },
    {
      title: '标签',
      dataIndex: 'label',
      key: 'label',
      width: 140,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: boolean, record: FlowItem) => (
        <Switch
          checked={status}
          onChange={(checked) => handleStatusChange(checked, record)}
          size="small"
        />
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 100,
    },
    {
      title: '发布时间',
      dataIndex: 'releaseTime',
      key: 'releaseTime',
      width: 170,
      sorter: true,
      render: (time: string) => time || '未发布',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 200,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 170,
      sorter: true,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 170,
      sorter: true,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      width: 320,
      render: (_, record: FlowItem) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个流程吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
          <Button
            type="link"
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() => handleDesign(record)}
          >
            编排
          </Button>
          <Button
            type="link"
            size="small"
            icon={<FileTextOutlined />}
            onClick={() => handleLogs(record)}
          >
            日志
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              刷新
            </Button>
            <FlowConfig>
              <Button icon={<SettingOutlined />}>
                配置
              </Button>
            </FlowConfig>
            <div style={{ flex: 1 }} />
            <Search
              placeholder="搜索名称、标签"
              allowClear
              style={{ width: 200 }}
              onSearch={handleSearch}
            />
          </Space>
        </Col>
        <Col span={24}>
          <Table
            columns={columns}
            dataSource={dataSource}
            loading={loading}
            rowKey="id"
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
            onChange={(paginationInfo, filters, sorter) => {
              setPagination(prev => ({
                ...prev,
                current: paginationInfo.current || 1,
                pageSize: paginationInfo.pageSize || 10,
              }));
              // TODO: 实现排序和筛选逻辑
            }}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default FlowList;
