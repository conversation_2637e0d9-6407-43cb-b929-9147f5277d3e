import { ReactNode, ComponentType } from 'react';

// 定义基础的 Node 和 Edge 类型，因为 @xyflow/react 没有直接导出这些类型
interface BaseNode {
  id: string;
  type?: string;
  position: { x: number; y: number };
  data?: any;
  width?: number;
  height?: number;
  selected?: boolean;
  dragging?: boolean;
  [key: string]: any;
}

interface BaseEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string | null;
  targetHandle?: string | null;
  type?: string;
  animated?: boolean;
  style?: React.CSSProperties;
  [key: string]: any;
}

/**
 * 流程字段定义
 */
export interface FlowField {
  field: string; // 自定义命名
  type?: string; // 字段类型 string number ...
  value?: string; // 自定义值
  name?: string; // 选择的变量名
  nodeId?: string; // 选择的节点ID
  nodeType?: string; // 选择的节点类型 start end ...
  label?: string; // 显示名称
  required?: boolean; // 是否必填
  [key: string]: any;
}

/**
 * 流程数据定义
 */
export interface FlowData {
  inputParams?: FlowField[];
  outputParams?: FlowField[];
  options?: any;
}

/**
 * 表单项配置
 */
export interface FormItem {
  label?: string;
  prop?: string;
  component?: {
    name?: string;
    vm?: ComponentType<any>;
    props?: Record<string, any>;
  };
  children?: FormItem[];
  hidden?: boolean;
  [key: string]: any;
}

/**
 * 流程节点定义 (扩展 React Flow 的 Node)
 */
export interface FlowNode extends BaseNode {
  enable?: boolean;
  label?: string;
  description?: string;
  icon?: string;
  color?: string;
  group?: string;
  name?: `node-${string}`;
  form?: {
    width?: string;
    focus?: string;
    items: FormItem[];
  };
  handle?: {
    target?: boolean;
    source?: boolean;
    next?: { label: string; value: string; [key: string]: any }[];
  };
  data?: FlowData;
  validator?(data: FlowData): void | string;
  component?: ComponentType<any>;
  cardWidth?: string;
  [key: string]: any;
}

/**
 * 流程边定义 (扩展 React Flow 的 Edge)
 */
export interface FlowEdge extends BaseEdge {
  animated?: boolean;
  style?: {
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 * 节点执行结果
 */
export interface FlowNodeResult {
  msgType: 'llmStream' | 'tool' | 'node' | 'flow';
  data: {
    status: 'done' | 'running' | 'start' | 'end';
    nodeId: string;
    nodeType: string;
    duration: number;
    name?: string;
    type?: string;
    isEnd: boolean;
    content?: string;
    isThinking?: boolean;
    result?: {
      success: string;
      error: string;
      result?: any;
    };
    count: {
      tokenUsage: number;
    };
    nextNodeIds: string[];
    reason?: 'success' | 'cancel' | 'error';
  };
}

/**
 * 流程信息
 */
export interface FlowInfo {
  id?: number;
  name?: string;
  label?: string;
  description?: string;
  status?: boolean;
  version?: string;
  releaseTime?: string;
  createTime?: string;
  updateTime?: string;
  [key: string]: any;
}

/**
 * 控制模式
 */
export type ControlMode = 'hand' | 'pointer';

/**
 * 视图配置
 */
export interface ViewportConfig {
  x: number;
  y: number;
  zoom: number;
}

/**
 * 节点配置
 */
export interface NodeConfig {
  group: string;
  label: string;
  description: string;
  color: string;
  component: ComponentType<any>;
  form: {
    width?: string;
    focus?: string;
    items: FormItem[];
  };
  data: FlowData;
  validator?(data: FlowData): void | string;
  enable?: boolean;
  type?: string;
  name?: string;
  icon?: string;
  cardWidth?: string;
}

/**
 * 事件类型
 */
export interface FlowEvents {
  'flow.openForm': (nodeId: string) => void;
  'flow.closeForm': () => void;
  'flow.runOpen': (node?: FlowNode) => void;
  'flow.nodeUpdate': (node: FlowNode) => void;
  'flow.save': () => void;
  'flow.clear': () => void;
  'flow.arrange': () => void;
}

/**
 * 选择框状态
 */
export interface SelectionState {
  show: boolean;
  left: number;
  top: number;
  width: number;
  height: number;
}

/**
 * 鼠标状态
 */
export interface MouseState {
  show: boolean;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  x: number;
  y: number;
  left: number;
  top: number;
  width: number;
  height: number;
}

/**
 * 历史记录项
 */
export interface HistoryItem {
  nodes: FlowNode[];
  edges: FlowEdge[];
  timestamp: number;
}

/**
 * 流程运行状态
 */
export interface FlowRunStatus {
  isRunning: boolean;
  currentNodeId?: string;
  results: Map<string, FlowNodeResult>;
}

// 所有类型都已经通过 export 关键字直接导出
