/* Flow 模块样式 - 转换自 Vue 版本的 SCSS */

/* Ant Design Popover 样式覆盖 */
.ant-popover.cl-flow__popper {
  border-radius: 8px !important;
}

.ant-popover.cl-flow__popper .ant-popover-arrow {
  display: none;
}

.ant-popover.cl-flow__popper .ant-scrollbar__view {
  padding: 0;
}

.ant-popover.cl-flow__popper .ant-select-dropdown__item {
  border-radius: 6px;
  height: 30px;
  font-size: 12px;
  line-height: 30px;
}

.ant-popover.cl-flow__popper.ant-select__popper {
  margin: -7px 0;
}

.ant-popover.cl-flow__popper.ant-select__popper .ant-select-dropdown__wrap {
  padding: 5px;
}

.ant-popover.cl-flow__popper:not(.ant-select__popper) {
  padding: 5px;
}

.ant-popover.cl-flow__popper.not-padding {
  padding: 0;
}

/* Flow 按钮图标样式 */
.cl-flow__btn-icon {
  color: var(--ant-color-text-secondary);
  cursor: pointer;
  border-radius: 6px;
  font-size: 14px;
  flex-shrink: 0;
  outline: none;
  box-sizing: content-box;
  padding: 4px;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.cl-flow__btn-icon:focus,
.cl-flow__btn-icon:hover {
  background-color: var(--ant-color-fill-tertiary);
  color: var(--ant-color-text);
}

.cl-flow__btn-icon.is-rt {
  position: absolute;
  top: -25px;
  right: 0;
}

.cl-flow__btn-icon.is-bg {
  background-color: var(--ant-color-fill-quaternary);
}

.cl-flow__btn-icon.is-bg:hover {
  color: var(--ant-color-primary) !important;
}

.cl-flow__btn-icon.is-active {
  background-color: var(--ant-color-bg-container) !important;
  color: var(--ant-color-primary) !important;
  box-shadow: 0 0 0 1px var(--ant-color-primary) !important;
}

/* Flow 内部项目样式 */
.cl-flow__inner-item {
  display: flex;
  align-items: center;
  border: 1px solid var(--ant-color-border);
  border-radius: var(--ant-border-radius);
  padding: 0 10px;
  cursor: pointer;
  height: 32px;
  width: 100%;
  position: relative;
  transition: all 0.2s;
  box-sizing: border-box;
}

.cl-flow__inner-item .text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 20px;
  color: var(--ant-color-text-secondary);
}

.cl-flow__inner-item .placeholder {
  color: var(--ant-color-text-quaternary);
  font-size: 14px;
}

.cl-flow__inner-item .close {
  position: absolute;
  right: 6px;
  display: none;
  font-size: 12px !important;
  color: var(--ant-color-text-tertiary);
}

.cl-flow__inner-item:hover {
  border-color: var(--ant-color-border-secondary);
}

.cl-flow__inner-item:hover .close {
  display: block;
}

/* Flow 文本区域项目样式 */
.cl-flow__textarea-item {
  border: 1px solid var(--ant-color-border);
  padding: 0 0 10px 0;
  border-radius: var(--ant-border-radius);
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.cl-flow__textarea-item .ant-input {
  background-color: transparent;
  box-shadow: none;
  padding: 0 10px;
}

.cl-flow__textarea-item .head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 40px;
  line-height: normal;
}

.cl-flow__textarea-item .head span {
  font-size: 12px;
  color: var(--ant-color-text-tertiary);
}

.cl-flow__textarea-item:last-child {
  margin-bottom: 0;
}

.cl-flow__textarea-item:hover {
  border-color: var(--ant-color-border-secondary);
}

/* Tools Card 样式 */
.tools-card {
  position: relative;
  background: #fff;
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tools-card:hover {
  border-color: var(--ant-color-primary);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tools-card.is-active {
  border-color: var(--ant-color-primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.tools-card.is-running {
  border-color: var(--ant-color-warning);
  background-color: var(--ant-color-warning-bg);
}

.tools-card.is-success {
  border-color: var(--ant-color-success);
  background-color: var(--ant-color-success-bg);
}

.tools-card.is-error {
  border-color: var(--ant-color-error);
  background-color: var(--ant-color-error-bg);
}

/* Tools Handle 样式 */
.tools-handle {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid var(--ant-color-primary);
  background-color: #fff;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s;
}

.tools-handle.is-link {
  background-color: var(--ant-color-primary);
}

.tools-handle.is-source {
  /* 源连接点特定样式 */
}

.tools-handle.is-target {
  /* 目标连接点特定样式 */
}

.tools-handle:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* Tools Panel 样式 */
.tools-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 400px;
  border-radius: 12px;
  background-color: var(--ant-color-bg-container);
  position: relative;
  box-shadow: 0px 0 10px 1px rgba(16, 24, 40, 0.05);
  margin-left: 10px;
}

.tools-panel__head {
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 50px;
  flex-shrink: 0;
  border-bottom: 1px solid var(--ant-color-border);
}

.tools-panel__head .label {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: var(--ant-color-text);
  letter-spacing: 1px;
}

.tools-panel__head .btns {
  display: flex;
  align-items: center;
}

.tools-panel__head .btns .cl-flow__btn-icon {
  margin-left: 6px;
}

.tools-panel__container {
  position: relative;
  flex: 1;
  overflow: hidden;
}

/* React Flow 面板样式 */
.react-flow__panel {
  display: flex;
}

.react-flow__panel.right {
  height: calc(100% - 100px);
  z-index: 10;
  margin: 0 10px 0 0;
  margin-top: 50px;
}

/* 选择框样式 */
.mouse-selection {
  position: fixed;
  border: 1px dashed var(--ant-color-primary);
  background-color: rgba(24, 144, 255, 0.1);
  pointer-events: none;
  z-index: 1000;
}

.node-selection {
  position: absolute;
  border: 2px solid var(--ant-color-primary);
  background-color: rgba(24, 144, 255, 0.1);
  cursor: move;
  z-index: 100;
}

/* 节点内容样式 */
.node-content {
  transition: font-size 0.1s;
}

/* 字段项样式 */
.field-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  font-size: 12px;
}

.field-label {
  font-weight: 500;
  margin-right: 4px;
}

.field-type {
  color: var(--ant-color-text-tertiary);
  font-size: 11px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .tools-panel {
    width: 300px;
  }
  
  .tools-card {
    min-width: 150px;
  }
}
