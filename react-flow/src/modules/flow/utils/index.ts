import dayjs from 'dayjs';
import { FlowNode, FlowEdge } from '../types';

/**
 * 格式化时间显示
 * @param dateString 日期字符串或Date对象
 * @param locale 语言环境，默认为中文
 * @returns 格式化后的时间字符串
 */
export function formatTime(dateString?: Date | string, locale: string = 'zh-cn'): string {
  if (!dateString) {
    return '';
  }

  const now = dayjs();
  const date = dayjs(dateString);

  let secondsDiff = now.diff(date, 'second');

  if (secondsDiff < 60) {
    if (secondsDiff < 0) {
      secondsDiff = 0;
    }

    return locale === 'en' 
      ? `${secondsDiff || 1} seconds ago`
      : `${secondsDiff || 1}秒前`;
  }

  const minutesDiff = now.diff(date, 'minute');
  if (minutesDiff < 60) {
    return locale === 'en'
      ? `${minutesDiff} minutes ago`
      : `${minutesDiff}分钟前`;
  }

  const hoursDiff = now.diff(date, 'hour');
  if (hoursDiff < 24) {
    return locale === 'en'
      ? `${hoursDiff} hours ago`
      : `${hoursDiff}小时前`;
  }

  const isYesterday = now.subtract(1, 'day').isSame(date, 'day');
  if (isYesterday) {
    return locale === 'en'
      ? `Yesterday ${date.format('HH:mm')}`
      : `昨天 ${date.format('HH:mm')}`;
  }

  return date.format('YYYY-MM-DD HH:mm');
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID字符串
 */
export function generateId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }

  return obj;
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 验证节点数据
 * @param node 节点数据
 * @returns 验证结果
 */
export function validateNode(node: FlowNode): { valid: boolean; message?: string } {
  if (!node.id) {
    return { valid: false, message: '节点ID不能为空' };
  }

  if (!node.type) {
    return { valid: false, message: '节点类型不能为空' };
  }

  if (!node.label) {
    return { valid: false, message: '节点标签不能为空' };
  }

  // 如果节点有验证器，执行验证
  if (node.validator && node.data) {
    try {
      const result = node.validator(node.data);
      if (result) {
        return { valid: false, message: result };
      }
    } catch (error) {
      return { valid: false, message: '节点验证失败' };
    }
  }

  return { valid: true };
}

/**
 * 验证流程数据
 * @param nodes 节点列表
 * @param edges 边列表
 * @returns 验证结果
 */
export function validateFlow(nodes: FlowNode[], edges: FlowEdge[]): { valid: boolean; message?: string } {
  // 检查是否有开始节点
  const startNodes = nodes.filter(node => node.type === 'start');
  if (startNodes.length === 0) {
    return { valid: false, message: '流程必须包含开始节点' };
  }

  if (startNodes.length > 1) {
    return { valid: false, message: '流程只能包含一个开始节点' };
  }

  // 检查是否有结束节点
  const endNodes = nodes.filter(node => node.type === 'end');
  if (endNodes.length === 0) {
    return { valid: false, message: '流程必须包含结束节点' };
  }

  // 验证每个节点
  for (const node of nodes) {
    const nodeValidation = validateNode(node);
    if (!nodeValidation.valid) {
      return { valid: false, message: `节点 ${node.label}: ${nodeValidation.message}` };
    }
  }

  // 检查是否有孤立节点（除了开始和结束节点）
  const connectedNodeIds = new Set<string>();
  edges.forEach(edge => {
    connectedNodeIds.add(edge.source);
    connectedNodeIds.add(edge.target);
  });

  const isolatedNodes = nodes.filter(node => 
    !connectedNodeIds.has(node.id) && 
    !['start', 'end'].includes(node.type!)
  );

  if (isolatedNodes.length > 0) {
    return { 
      valid: false, 
      message: `存在孤立节点: ${isolatedNodes.map(n => n.label).join(', ')}` 
    };
  }

  return { valid: true };
}

/**
 * 计算节点之间的距离
 * @param node1 节点1
 * @param node2 节点2
 * @returns 距离
 */
export function calculateDistance(node1: FlowNode, node2: FlowNode): number {
  const pos1 = node1.position || { x: 0, y: 0 };
  const pos2 = node2.position || { x: 0, y: 0 };
  
  return Math.sqrt(
    Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2)
  );
}

/**
 * 获取节点的边界框
 * @param node 节点
 * @returns 边界框信息
 */
export function getNodeBounds(node: FlowNode): { 
  x: number; 
  y: number; 
  width: number; 
  height: number; 
} {
  const position = node.position || { x: 0, y: 0 };
  const width = node.width || 200;
  const height = node.height || 100;
  
  return {
    x: position.x,
    y: position.y,
    width,
    height
  };
}

/**
 * 检查两个节点是否重叠
 * @param node1 节点1
 * @param node2 节点2
 * @returns 是否重叠
 */
export function isNodesOverlapping(node1: FlowNode, node2: FlowNode): boolean {
  const bounds1 = getNodeBounds(node1);
  const bounds2 = getNodeBounds(node2);
  
  return !(
    bounds1.x + bounds1.width < bounds2.x ||
    bounds2.x + bounds2.width < bounds1.x ||
    bounds1.y + bounds1.height < bounds2.y ||
    bounds2.y + bounds2.height < bounds1.y
  );
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 下载文件
 * @param content 文件内容
 * @param filename 文件名
 * @param contentType 内容类型
 */
export function downloadFile(content: string, filename: string, contentType: string = 'application/json'): void {
  const blob = new Blob([content], { type: contentType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
}
