// Flow 模块配置

export interface FlowModuleConfig {
  // 是否启用调试模式
  debug?: boolean;
  
  // 默认语言
  defaultLocale?: 'zh-cn' | 'zh-tw' | 'en';
  
  // API 基础路径
  apiBaseUrl?: string;
  
  // 节点配置
  nodes?: {
    // 默认节点宽度
    defaultWidth?: number;
    // 默认节点高度
    defaultHeight?: number;
    // 节点间距
    spacing?: {
      x?: number;
      y?: number;
    };
  };
  
  // 画布配置
  canvas?: {
    // 默认缩放级别
    defaultZoom?: number;
    // 最小缩放级别
    minZoom?: number;
    // 最大缩放级别
    maxZoom?: number;
    // 是否显示网格
    showGrid?: boolean;
    // 网格大小
    gridSize?: number;
  };
  
  // 自动保存配置
  autoSave?: {
    // 是否启用自动保存
    enabled?: boolean;
    // 自动保存间隔（毫秒）
    interval?: number;
  };
}

// 默认配置
export const defaultConfig: FlowModuleConfig = {
  debug: false,
  defaultLocale: 'zh-cn',
  apiBaseUrl: '/api',
  nodes: {
    defaultWidth: 200,
    defaultHeight: 100,
    spacing: {
      x: 400,
      y: 150
    }
  },
  canvas: {
    defaultZoom: 1.0,
    minZoom: 0.5,
    maxZoom: 1.5,
    showGrid: true,
    gridSize: 16
  },
  autoSave: {
    enabled: true,
    interval: 30000 // 30秒
  }
};

// 配置管理器
class FlowConfigManager {
  private config: FlowModuleConfig;

  constructor(initialConfig: FlowModuleConfig = {}) {
    this.config = { ...defaultConfig, ...initialConfig };
  }

  // 获取配置
  get<K extends keyof FlowModuleConfig>(key: K): FlowModuleConfig[K] {
    return this.config[key];
  }

  // 设置配置
  set<K extends keyof FlowModuleConfig>(key: K, value: FlowModuleConfig[K]): void {
    this.config[key] = value;
  }

  // 更新配置
  update(newConfig: Partial<FlowModuleConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // 获取完整配置
  getAll(): FlowModuleConfig {
    return { ...this.config };
  }

  // 重置为默认配置
  reset(): void {
    this.config = { ...defaultConfig };
  }
}

// 导出配置管理器实例
export const flowConfig = new FlowConfigManager();

// 导出配置管理器类
export { FlowConfigManager };
