import zhCN from './zh-cn.json';
import zhTW from './zh-tw.json';
import en from './en.json';

export const locales = {
  'zh-cn': zhCN,
  'zh-tw': zhTW,
  'en': en
};

export type LocaleKey = keyof typeof locales;

export const defaultLocale: LocaleKey = 'zh-cn';

export const supportedLocales: Array<{ label: string; value: LocaleKey }> = [
  { label: '中文', value: 'zh-cn' },
  { label: '繁体中文', value: 'zh-tw' },
  { label: 'English', value: 'en' }
];

// 简单的翻译函数
export function t(key: string, locale: LocaleKey = defaultLocale, params?: Record<string, any>): string {
  const messages = locales[locale] || locales[defaultLocale];
  let message = (messages as any)[key] || key;
  
  // 简单的参数替换
  if (params) {
    Object.keys(params).forEach(paramKey => {
      message = message.replace(`{${paramKey}}`, params[paramKey]);
    });
  }
  
  return message;
}

// React Hook for internationalization
import React, { useState, useCallback, createContext, useContext } from 'react';

interface I18nContextType {
  locale: LocaleKey;
  setLocale: (locale: LocaleKey) => void;
  t: (key: string, params?: Record<string, any>) => string;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

export const useI18n = () => {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};

export const I18nProvider = ({
  children,
  initialLocale = defaultLocale
}: { children: React.ReactNode; initialLocale?: LocaleKey }) => {
  const [locale, setLocale] = useState<LocaleKey>(initialLocale);

  const translate = useCallback((key: string, params?: Record<string, any>) => {
    return t(key, locale, params);
  }, [locale]);

  return React.createElement(I18nContext.Provider, {
    value: { locale, setLocale, t: translate }
  }, children);
};
