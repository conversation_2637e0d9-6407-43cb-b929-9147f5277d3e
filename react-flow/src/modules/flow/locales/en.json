{"必填": "Required", "变量名称已存在": "Variable name already exists", "节点运行信息": "Node running information", "节点": "Node", "标签（可以根据标签调用）": "Label (can be called according to the label)", "流程ID": "Process ID", "类型 0-失败 1-成功 2-未知": "Type 0 - Failure 1 - Success 2 - Unknown", "配置": "Configuration", "添加": "Add", "暂无描述": "No description", "重置": "Reset", "类型": "Type", "名称": "Name", "描述": "Description", "保存成功": "Save successfully", "设置的输入可在工作流程中使用": "The set input can be used in the workflow", "立即添加": "Add immediately", "添加变量": "Add variable", "字段类型": "Field type", "文本": "Text", "数字": "Number", "图片": "Picture", "文件": "File", "变量名称": "Variable name", "请输入变量名称": "Please enter the variable name", "只能由字母、数字、下划线组成，且以字母开头": "It can only be composed of letters, numbers, and underscores and must start with a letter", "显示名称": "Display name", "保存": "Save", "运行": "Run", "历史记录": "History", "导入": "Import", "导出": "Export", "删除": "Delete", "复制": "Copy", "编辑": "Edit", "调试": "Debug", "新增": "Add", "刷新": "Refresh", "编排": "Design", "日志": "Logs", "确定": "OK", "取消": "Cancel", "开始": "Start", "结束": "End", "流程": "Flow", "执行代码": "Execute Code", "分类器": "Classifier", "未保存": "Unsaved", "已保存": "Saved", "运行中": "Running", "成功": "Success", "失败": "Failed", "等待": "Waiting", "停止": "Stop", "清空": "Clear", "自适应": "Fit View", "整理节点": "<PERSON><PERSON><PERSON>", "添加节点": "Add Node", "缩略图": "Minimap", "切换交互模式": "Toggle Interaction Mode", "粘贴到这里": "Paste Here", "流程运行": "Run Flow", "清空节点": "Clear Nodes", "复制成功": "Copy Success", "删除成功": "Delete Success", "状态更新成功": "Status Update Success", "加载数据失败": "Failed to Load Data", "删除失败": "Delete Failed", "状态更新失败": "Status Update Failed", "保存失败": "Save Failed", "确定要删除这个流程吗？": "Are you sure to delete this flow?", "请确认是否清空所有节点？": "Are you sure to clear all nodes?", "提示": "Tip", "搜索名称、标签": "Search Name, Label", "共 {total} 条记录": "Total {total} records", "版本": "Version", "发布时间": "Release Time", "创建时间": "Create Time", "更新时间": "Update Time", "操作": "Actions", "状态": "Status", "未发布": "Unpublished", "正在运行中...": "Running...", "点击运行按钮开始调试": "Click run button to start debugging"}