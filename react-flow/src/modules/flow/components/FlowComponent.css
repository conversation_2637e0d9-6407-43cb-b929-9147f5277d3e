.cl-flow {
  height: 100%;
}

.cl-flow .react-flow {
  height: 100%;
}

/* 向节点添加动画 */
.cl-flow .react-flow__node {
  transition: all 0.1s;
}

.cl-flow .react-flow__node.animation {
  transition: all 0.1s;
}

.cl-flow .react-flow__node .node-content {
  transition: font-size 0.1s;
}

/* Flow 按钮图标样式 */
.cl-flow__btn-icon {
  color: var(--ant-color-text-secondary);
  cursor: pointer;
  border-radius: 6px;
  font-size: 14px;
  flex-shrink: 0;
  outline: none;
  box-sizing: content-box;
  padding: 4px;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.cl-flow__btn-icon:focus,
.cl-flow__btn-icon:hover {
  background-color: var(--ant-color-fill-tertiary);
  color: var(--ant-color-text);
}

.cl-flow__btn-icon.is-rt {
  position: absolute;
  top: -25px;
  right: 0;
}

.cl-flow__btn-icon.is-bg {
  background-color: var(--ant-color-fill-quaternary);
}

.cl-flow__btn-icon.is-bg:hover {
  color: var(--ant-color-primary) !important;
}

.cl-flow__btn-icon.is-active {
  background-color: var(--ant-color-bg-container) !important;
  color: var(--ant-color-primary) !important;
  box-shadow: 0 0 0 1px var(--ant-color-primary) !important;
}

/* Flow 内部项目样式 */
.cl-flow__inner-item {
  display: flex;
  align-items: center;
  border: 1px solid var(--ant-color-border);
  border-radius: var(--ant-border-radius);
  padding: 0 10px;
  cursor: pointer;
  height: 32px;
  width: 100%;
  position: relative;
  transition: all 0.2s;
  box-sizing: border-box;
}

.cl-flow__inner-item .text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 20px;
  color: var(--ant-color-text-secondary);
}

.cl-flow__inner-item .placeholder {
  color: var(--ant-color-text-quaternary);
  font-size: 14px;
}

.cl-flow__inner-item .close {
  position: absolute;
  right: 6px;
  display: none;
  font-size: 12px !important;
  color: var(--ant-color-text-tertiary);
}

.cl-flow__inner-item:hover {
  border-color: var(--ant-color-border-secondary);
}

.cl-flow__inner-item:hover .close {
  display: block;
}

/* Flow 文本区域项目样式 */
.cl-flow__textarea-item {
  border: 1px solid var(--ant-color-border);
  padding: 0 0 10px 0;
  border-radius: var(--ant-border-radius);
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.cl-flow__textarea-item .ant-input {
  background-color: transparent;
  box-shadow: none;
  padding: 0 10px;
}

.cl-flow__textarea-item .head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 40px;
  line-height: normal;
}

.cl-flow__textarea-item .head span {
  font-size: 12px;
  color: var(--ant-color-text-tertiary);
}

.cl-flow__textarea-item:last-child {
  margin-bottom: 0;
}

.cl-flow__textarea-item:hover {
  border-color: var(--ant-color-border-secondary);
}
