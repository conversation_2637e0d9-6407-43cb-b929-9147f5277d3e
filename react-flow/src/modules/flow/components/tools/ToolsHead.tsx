import React, { useCallback } from 'react';
import { Panel } from '@xyflow/react';
import { Tooltip, Space, message } from 'antd';
import { 
  SaveOutlined, 
  PlayCircleOutlined, 
  HistoryOutlined,
  ImportOutlined,
  ExportOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useFlow } from '../../hooks';
import FlowConfig from '../FlowConfig';
import dayjs from 'dayjs';

const ToolsHead: React.FC = () => {
  const flow = useFlow();

  // 运行流程
  const handleRun = useCallback(() => {
    // TODO: 实现运行逻辑
    console.log('Run flow');
  }, []);

  // 保存流程
  const handleSave = useCallback(async () => {
    try {
      await flow.save();
      message.success('数据保存成功');
    } catch (error) {
      message.error('保存失败');
    }
  }, [flow]);

  // 历史记录
  const handleHistory = useCallback(() => {
    // TODO: 实现历史记录逻辑
    console.log('Show history');
  }, []);

  // 导入流程
  const handleImport = useCallback(() => {
    // TODO: 实现导入逻辑
    console.log('Import flow');
  }, []);

  // 导出流程
  const handleExport = useCallback(() => {
    // TODO: 实现导出逻辑
    console.log('Export flow');
  }, []);

  const formatTime = (dateString?: string): string => {
    if (!dateString) return '未保存';
    return `已保存 ${dayjs(dateString).format('MM-DD HH:mm:ss')}`;
  };

  return (
    <Panel position="top-left" className="tools-head">
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(8px)',
          borderRadius: '8px',
          padding: '8px 16px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          minWidth: '400px'
        }}
      >
        <div className="info">
          <div className="title" style={{ fontWeight: 'bold', fontSize: '14px' }}>
            {flow.flowInfo?.name}（{flow.flowInfo?.label}）
          </div>
          <div className="desc" style={{ fontSize: '12px', color: '#666' }}>
            {formatTime(flow.flowInfo?.updateTime)}
          </div>
        </div>

        <div className="op">
          <Space size="small">
            <Tooltip title="保存" placement="bottom">
              <div className="item cl-flow__btn-icon" onClick={handleSave}>
                <SaveOutlined />
              </div>
            </Tooltip>

            <Tooltip title="运行" placement="bottom">
              <div className="item cl-flow__btn-icon" onClick={handleRun}>
                <PlayCircleOutlined />
              </div>
            </Tooltip>

            <Tooltip title="历史记录" placement="bottom">
              <div className="item cl-flow__btn-icon" onClick={handleHistory}>
                <HistoryOutlined />
              </div>
            </Tooltip>

            <Tooltip title="导入" placement="bottom">
              <div className="item cl-flow__btn-icon" onClick={handleImport}>
                <ImportOutlined />
              </div>
            </Tooltip>

            <Tooltip title="导出" placement="bottom">
              <div className="item cl-flow__btn-icon" onClick={handleExport}>
                <ExportOutlined />
              </div>
            </Tooltip>

            <FlowConfig>
              <Tooltip title="配置" placement="bottom">
                <div className="item cl-flow__btn-icon">
                  <SettingOutlined />
                </div>
              </Tooltip>
            </FlowConfig>
          </Space>
        </div>
      </div>
    </Panel>
  );
};

export default ToolsHead;
