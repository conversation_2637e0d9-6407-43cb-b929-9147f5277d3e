import React from 'react';

// 内联类型定义
interface FlowField {
  field: string;
  type?: string;
  value?: string;
  label?: string;
  required?: boolean;
  [key: string]: any;
}

interface ToolsFieldsProps {
  value?: FlowField[];
  onChange?: (value: FlowField[]) => void;
  disabled?: boolean;
}

const ToolsFields: React.FC<ToolsFieldsProps> = ({
  value = [],
  onChange,
  disabled = false
}) => {
  return (
    <div className="tools-fields">
      {value.map((field, index) => (
        <div key={index} className="field-item">
          <span className="field-label">{field.label || field.field}</span>
          <span className="field-type">({field.type || 'text'})</span>
        </div>
      ))}
    </div>
  );
};

export default ToolsFields;
