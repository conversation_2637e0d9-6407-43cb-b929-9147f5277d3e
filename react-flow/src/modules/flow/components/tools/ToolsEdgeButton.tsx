import React from 'react';
import { Button } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

interface ToolsEdgeButtonProps {
  id: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  sourcePosition: string;
  targetPosition: string;
  markerEnd?: string;
  style?: React.CSSProperties;
  data?: any;
}

const ToolsEdgeButton: React.FC<ToolsEdgeButtonProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  markerEnd,
  style,
  data
}) => {
  // 计算按钮位置（边的中点）
  const centerX = (sourceX + targetX) / 2;
  const centerY = (sourceY + targetY) / 2;

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: 实现删除边的逻辑
    console.log('Delete edge:', id);
  };

  return (
    <foreignObject
      x={centerX - 10}
      y={centerY - 10}
      width={20}
      height={20}
      className="edge-button"
    >
      <Button
        type="text"
        size="small"
        icon={<DeleteOutlined />}
        onClick={handleDelete}
        style={{
          width: '20px',
          height: '20px',
          padding: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#fff',
          border: '1px solid #d9d9d9',
          borderRadius: '50%',
          fontSize: '10px',
          opacity: 0.8
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.opacity = '1';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.opacity = '0.8';
        }}
      />
    </foreignObject>
  );
};

export default ToolsEdgeButton;
