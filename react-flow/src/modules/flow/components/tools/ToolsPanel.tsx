import React from 'react';
import { Panel } from '@xyflow/react';
import ToolsPanelForm from './panel/ToolsPanelForm';
import ToolsPanelRun from './panel/ToolsPanelRun';

const ToolsPanel: React.FC = () => {
  return (
    <Panel position="top-right" className="tools-panel-right">
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(100vh - 100px)',
          marginTop: '50px',
          marginRight: '10px'
        }}
      >
        <ToolsPanelForm />
        <ToolsPanelRun />
      </div>
    </Panel>
  );
};

export default ToolsPanel;
