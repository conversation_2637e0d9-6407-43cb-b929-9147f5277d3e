import React, { useMemo, CSSProperties } from 'react';
import { Handle, Position, type Connection } from '@xyflow/react';
import { RightOutlined } from '@ant-design/icons';
import { useFlow } from '../../hooks';

interface ToolsHandleProps {
  nodeId?: string;
  id?: string;
  position?: CSSProperties;
  type?: 'target' | 'source';
}

const ToolsHandle: React.FC<ToolsHandleProps> = ({
  nodeId,
  id,
  position = {},
  type = 'source'
}) => {
  const flow = useFlow();

  // 节点位置
  const align = useMemo(() => {
    return type === 'target' ? Position.Left : Position.Right;
  }, [type]);

  // 是否连接
  const isLink = useMemo(() => {
    return !!flow.edges.find(e => {
      if (e[type] === nodeId) {
        return e[`${type}Handle`] === id;
      }
      return false;
    });
  }, [flow.edges, type, nodeId, id]);

  // 连接校验
  const onValidConnection = (connection: Connection): boolean => {
    const { targetHandle, sourceHandle, source, target } = connection;

    // 不能连父节点
    const pNodes = flow.parentAllNodes(source!);
    if (pNodes.find(e => e.id === target)) {
      return false;
    }

    // 不能连自己
    if (target === source) {
      return false;
    }

    // 只能目标节点去连
    if (targetHandle === 'target' && sourceHandle !== 'target') {
      return true;
    }

    return false;
  };

  return (
    <div
      className={`tools-handle is-${type} ${isLink ? 'is-link' : ''}`}
      style={{
        position: 'absolute',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '18px',
        height: '18px',
        borderRadius: '50%',
        border: '2px solid #1890ff',
        backgroundColor: isLink ? '#1890ff' : '#fff',
        cursor: 'pointer',
        zIndex: 10,
        ...position
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <span 
        className="rod" 
        style={{
          position: 'absolute',
          width: '8px',
          height: '2px',
          backgroundColor: isLink ? '#fff' : '#1890ff',
          borderRadius: '1px'
        }}
      />

      <RightOutlined 
        className="icon" 
        style={{
          fontSize: '8px',
          color: isLink ? '#fff' : '#1890ff'
        }}
      />

      <Handle
        id={id}
        type={type}
        position={align}
        isValidConnection={onValidConnection}
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          border: 'none',
          background: 'transparent',
          opacity: 0
        }}
      />
    </div>
  );
};

export default ToolsHandle;
