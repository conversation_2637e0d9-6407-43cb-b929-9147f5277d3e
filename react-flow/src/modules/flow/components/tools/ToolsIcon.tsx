import React from 'react';
import { 
  PlayCircleOutlined, 
  StopOutlined, 
  CodeOutlined, 
  BranchesOutlined,
  ApiOutlined,
  FileTextOutlined,
  SettingOutlined
} from '@ant-design/icons';

interface ToolsIconProps {
  name?: string;
  color?: string;
  size?: number;
}

const iconMap: Record<string, React.ComponentType> = {
  'start': PlayCircleOutlined,
  'end': StopOutlined,
  'code': CodeOutlined,
  'flow': BranchesOutlined,
  'classify': ApiOutlined,
  'llm': FileTextOutlined,
  'judge': SettingOutlined,
  'know': FileTextOutlined,
  'json': CodeOutlined,
  'parse': FileTextOutlined,
  'variable': SettingOutlined
};

const ToolsIcon: React.FC<ToolsIconProps> = ({ 
  name = 'default', 
  color = '#1890ff', 
  size = 16 
}) => {
  const IconComponent = iconMap[name] || SettingOutlined;

  return (
    <div
      className="tools-icon"
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: size + 4,
        height: size + 4,
        borderRadius: '4px',
        backgroundColor: color + '20',
        color: color
      }}
    >
      <IconComponent style={{ fontSize: size }} />
    </div>
  );
};

export default ToolsIcon;
