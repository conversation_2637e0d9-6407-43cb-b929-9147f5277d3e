import React, { useMemo, useCallback } from 'react';
import { Tooltip } from 'antd';
import { DeleteOutlined, PlayCircleOutlined, MoreOutlined } from '@ant-design/icons';
import { useFlow } from '../../../hooks';
import { FlowNode } from '../../../types';
import ToolsHandle from '../ToolsHandle';
import ToolsIcon from '../ToolsIcon';
import ToolsMore from '../ToolsMore';

interface ToolsCardProps {
  nodeId: string;
}

const ToolsCard: React.FC<ToolsCardProps> = ({ nodeId }) => {
  const flow = useFlow();

  // 获取节点数据
  const node = useMemo(() => {
    return flow.findNode(nodeId);
  }, [flow, nodeId]);

  // 运行状态结果
  const result = useMemo(() => {
    // TODO: 实现运行状态逻辑
    return { status: 'idle' };
  }, []);

  // 能否删除
  const isDel = useMemo(() => {
    return !['start', 'end'].includes(node?.type || '');
  }, [node?.type]);

  // 是否开始节点
  const isStart = useMemo(() => {
    return node?.type === 'start';
  }, [node?.type]);

  // 是否激活
  const isActive = useMemo(() => {
    return nodeId === flow.selectedNode?.id;
  }, [nodeId, flow.selectedNode?.id]);

  // 节点组件
  const NodeComponent = useMemo(() => {
    return flow.CustomNodes.find(e => e.type === node?.type)?.component;
  }, [flow.CustomNodes, node?.type]);

  // 详情
  const handleDetail = useCallback(() => {
    // TODO: 实现打开表单逻辑
    console.log('Open form for node:', nodeId);
  }, [nodeId]);

  // 调试
  const handleRun = useCallback(() => {
    // TODO: 实现调试逻辑
    console.log('Run node:', node);
  }, [node]);

  // 移除节点
  const handleRemove = useCallback(() => {
    if (node) {
      flow.removeNodes(node);
    }
  }, [flow, node]);

  if (!node) {
    return null;
  }

  return (
    <div
      className={`tools-card is-${result.status} ${isActive ? 'is-active' : ''}`}
      onClick={handleDetail}
      style={{
        position: 'relative',
        background: '#fff',
        border: '1px solid #e8e8e8',
        borderRadius: '8px',
        padding: '12px',
        minWidth: '200px',
        cursor: 'pointer',
        transition: 'all 0.2s'
      }}
    >
      {/* 源选择点 */}
      {(node?.handle?.source ?? true) && (
        <ToolsHandle
          type="source"
          id="source"
          nodeId={nodeId}
          position={{
            right: '-9px',
            top: '14px'
          }}
        />
      )}

      {/* 目标选择点 */}
      {(node?.handle?.target ?? true) && (
        <ToolsHandle
          type="target"
          id="target"
          nodeId={nodeId}
          position={{
            left: '-9px',
            top: '14px'
          }}
        />
      )}

      <div className="head" style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
        <ToolsIcon name={node?.icon} color={node?.color} />
        <span style={{ marginLeft: '8px', flex: 1 }}>{node?.label}</span>

        <div className="btns" onClick={(e) => e.stopPropagation()}>
          {!isStart && (
            <>
              {flow.isRun(node) && (
                <Tooltip title="调试" placement="top">
                  <PlayCircleOutlined 
                    className="cl-flow__btn-icon" 
                    onClick={handleRun}
                    style={{ marginLeft: '4px' }}
                  />
                </Tooltip>
              )}

              {isDel && (
                <Tooltip title="删除" placement="top">
                  <DeleteOutlined 
                    className="cl-flow__btn-icon" 
                    onClick={handleRemove}
                    style={{ marginLeft: '4px' }}
                  />
                </Tooltip>
              )}
            </>
          )}

          <ToolsMore node={node}>
            <MoreOutlined className="cl-flow__btn-icon" style={{ marginLeft: '4px' }} />
          </ToolsMore>
        </div>
      </div>

      <div className="desc" style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
        {node?.desc || node?.description}
      </div>

      <div className="container">
        {NodeComponent && <NodeComponent node={node} />}
      </div>
    </div>
  );
};

export default ToolsCard;
