import React, { forwardRef, useImperative<PERSON><PERSON><PERSON>, useCallback } from 'react';
import { Dropdown, Menu, message } from 'antd';
import { CopyOutlined, DeleteOutlined } from '@ant-design/icons';
import { useFlow } from '../../hooks';
import { FlowNode } from '../../types';

interface ContextMenuRef {
  onPane: (event: React.MouseEvent) => void;
  onNode: (params: { event: React.MouseEvent; node: FlowNode }) => void;
  close: () => void;
}

const ToolsContextMenu = forwardRef<ContextMenuRef>((props, ref) => {
  const flow = useFlow();

  // 画布右键菜单
  const onPane = useCallback(async (event: React.MouseEvent) => {
    event.preventDefault();
    
    const clipboardNode = await flow.getClipboardNode();
    
    const menuItems = [
      {
        key: 'paste',
        label: '粘贴到这里',
        icon: <CopyOutlined />,
        disabled: !clipboardNode,
        onClick: () => {
          if (clipboardNode) {
            const rect = (event.target as Element).getBoundingClientRect();
            const { zoom } = flow.viewport;
            
            const node = flow.addNode(clipboardNode.type!, {
              ...clipboardNode,
              position: {
                x: (event.clientX - rect.left) / zoom,
                y: (event.clientY - rect.top) / zoom
              }
            });
            
            if (node) {
              flow.setNode(node);
              flow.setViewportByNode(node);
            }
          }
        }
      },
      {
        key: 'run',
        label: '流程运行',
        onClick: () => {
          // TODO: 实现流程运行逻辑
          console.log('Run flow');
        }
      },
      {
        key: 'arrange',
        label: '整理节点',
        onClick: () => {
          flow.arrange();
        }
      },
      {
        key: 'clear',
        label: '清空节点',
        onClick: () => {
          // TODO: 添加确认对话框
          flow.clear();
        }
      }
    ];

    // TODO: 实现实际的右键菜单显示逻辑
    console.log('Show pane context menu', menuItems);
  }, [flow]);

  // 节点右键菜单
  const onNode = useCallback(({ event, node }: { event: React.MouseEvent; node: FlowNode }) => {
    event.preventDefault();
    
    if (['start', 'end'].includes(node.type!)) {
      return;
    }

    const menuItems = [
      {
        key: 'copy',
        label: '复制',
        icon: <CopyOutlined />,
        onClick: () => {
          flow.copyNode(node);
          message.success('复制成功');
        }
      },
      {
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        onClick: () => {
          flow.removeNodes(node);
        }
      }
    ];

    // TODO: 实现实际的右键菜单显示逻辑
    console.log('Show node context menu', menuItems);
  }, [flow]);

  const close = useCallback(() => {
    // TODO: 实现关闭菜单逻辑
    console.log('Close context menu');
  }, []);

  useImperativeHandle(ref, () => ({
    onPane,
    onNode,
    close
  }));

  return null; // 实际的菜单会通过 Dropdown 或其他方式显示
});

ToolsContextMenu.displayName = 'ToolsContextMenu';

export default ToolsContextMenu;
