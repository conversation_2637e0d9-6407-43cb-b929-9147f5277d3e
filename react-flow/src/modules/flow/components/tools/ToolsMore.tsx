import React, { useState } from 'react';
import { Dropdown, Menu } from 'antd';
import { FlowNode } from '../../types';

interface ToolsMoreProps {
  node: FlowNode;
  children: React.ReactNode;
}

const ToolsMore: React.FC<ToolsMoreProps> = ({ node, children }) => {
  const [visible, setVisible] = useState(false);

  const menuItems = [
    {
      key: 'copy',
      label: '复制节点'
    },
    {
      key: 'duplicate',
      label: '复制并粘贴'
    },
    {
      key: 'export',
      label: '导出配置'
    }
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'copy':
        // TODO: 实现复制逻辑
        console.log('Copy node:', node);
        break;
      case 'duplicate':
        // TODO: 实现复制并粘贴逻辑
        console.log('Duplicate node:', node);
        break;
      case 'export':
        // TODO: 实现导出逻辑
        console.log('Export node:', node);
        break;
    }
    setVisible(false);
  };

  return (
    <Dropdown
      menu={{ items: menuItems, onClick: handleMenuClick }}
      trigger={['click']}
      open={visible}
      onOpenChange={setVisible}
      placement="bottomRight"
    >
      <div onClick={(e) => e.stopPropagation()}>
        {children}
      </div>
    </Dropdown>
  );
};

export default ToolsMore;
