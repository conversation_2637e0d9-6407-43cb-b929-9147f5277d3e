import React, { useState, useCallback, useEffect } from 'react';
import { useFlow } from '../../hooks';

// 内联类型定义，避免导入问题
interface FlowNode {
  id: string;
  type?: string;
  position: { x: number; y: number };
  data?: any;
  [key: string]: any;
}

interface MouseState {
  show: boolean;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  x: number;
  y: number;
  left: number;
  top: number;
  width: number;
  height: number;
}

interface SelectionState {
  show: boolean;
  left: number;
  top: number;
  width: number;
  height: number;
}

const ToolsSelection: React.FC = () => {
  const flow = useFlow();
  
  const [mouse, setMouse] = useState<MouseState>({
    show: false,
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
    x: 0,
    y: 0,
    left: 0,
    top: 0,
    width: 0,
    height: 0
  });

  const [box, setBox] = useState<SelectionState>({
    show: false,
    left: 0,
    top: 0,
    width: 0,
    height: 0
  });

  const [selection, setSelection] = useState({
    list: [] as FlowNode[],
    startX: 0,
    startY: 0,
    x: 0,
    y: 0,
    lock: false
  });

  // 鼠标按下
  const onMouseDown = useCallback((e: MouseEvent) => {
    if (e.target !== document.querySelector('.react-flow__pane')) {
      return;
    }

    setMouse(prev => ({
      ...prev,
      show: true,
      startX: e.pageX,
      startY: e.pageY,
      x: e.pageX,
      y: e.pageY
    }));

    const mousemove = (e: MouseEvent) => {
      setMouse(prev => ({
        ...prev,
        endX: e.pageX,
        endY: e.pageY,
        left: Math.min(prev.startX, e.pageX),
        top: Math.min(prev.startY, e.pageY),
        width: Math.abs(e.pageX - prev.startX),
        height: Math.abs(e.pageY - prev.startY)
      }));
    };

    const mouseup = () => {
      // TODO: 实现选择逻辑
      setMouse(prev => ({ ...prev, show: false }));
      document.removeEventListener('mousemove', mousemove);
      document.removeEventListener('mouseup', mouseup);
    };

    document.addEventListener('mousemove', mousemove);
    document.addEventListener('mouseup', mouseup);
  }, []);

  // 选择框鼠标按下
  const onSelectionMouseDown = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();

    setSelection(prev => ({
      ...prev,
      lock: true,
      startX: e.pageX,
      startY: e.pageY
    }));

    const mousemove = (e: MouseEvent) => {
      // TODO: 实现拖拽选中节点逻辑
    };

    const mouseup = () => {
      setSelection(prev => ({ ...prev, lock: false }));
      document.removeEventListener('mousemove', mousemove);
      document.removeEventListener('mouseup', mouseup);
    };

    document.addEventListener('mousemove', mousemove);
    document.addEventListener('mouseup', mouseup);
  }, []);

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelection(prev => ({ ...prev, list: [] }));
    setBox(prev => ({ ...prev, show: false }));
  }, []);

  useEffect(() => {
    document.addEventListener('mousedown', onMouseDown);
    return () => {
      document.removeEventListener('mousedown', onMouseDown);
    };
  }, [onMouseDown]);

  return (
    <>
      {/* 鼠标选择框 */}
      {mouse.show && (
        <div
          className="mouse-selection"
          style={{
            position: 'fixed',
            left: mouse.left,
            top: mouse.top,
            width: mouse.width,
            height: mouse.height,
            border: '1px dashed #1890ff',
            backgroundColor: 'rgba(24, 144, 255, 0.1)',
            pointerEvents: 'none',
            zIndex: 1000
          }}
        />
      )}

      {/* 节点选择框 */}
      {box.show && (
        <div
          className="node-selection"
          style={{
            position: 'absolute',
            left: box.left,
            top: box.top,
            width: box.width,
            height: box.height,
            border: '2px solid #1890ff',
            backgroundColor: 'rgba(24, 144, 255, 0.1)',
            cursor: 'move',
            zIndex: 100
          }}
          onMouseDown={onSelectionMouseDown}
        />
      )}
    </>
  );
};

export default ToolsSelection;
