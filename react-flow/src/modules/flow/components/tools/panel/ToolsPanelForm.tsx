import React, { useState, useEffect, useMemo } from 'react';
import { Input, Form, Button, Space } from 'antd';
import { PlayCircleOutlined, CloseOutlined } from '@ant-design/icons';
import { useFlow } from '../../../hooks';

// 内联类型定义
interface FlowNode {
  id: string;
  type?: string;
  label?: string;
  description?: string;
  icon?: string;
  color?: string;
  data?: any;
  form?: {
    width?: string;
    items?: any[];
  };
  [key: string]: any;
}
import ToolsIcon from '../ToolsIcon';

const ToolsPanelForm: React.FC = () => {
  const flow = useFlow();
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [nodeId, setNodeId] = useState<string>();

  // 当前节点
  const node = useMemo(() => {
    return nodeId ? flow.findNode(nodeId) : undefined;
  }, [flow, nodeId]);

  // 最小宽度
  const width = useMemo(() => {
    return node?.form?.width || '400px';
  }, [node?.form?.width]);

  // 监听选中节点变化
  useEffect(() => {
    if (flow.selectedNode) {
      openForm(flow.selectedNode.id);
    } else {
      closeForm();
    }
  }, [flow.selectedNode]);

  // 打开表单
  const openForm = (id: string) => {
    setNodeId(id);
    setVisible(true);

    const selectedNode = flow.findNode(id);
    if (selectedNode) {
      // 设置表单初始值
      form.setFieldsValue(selectedNode.data || {});
    }
  };

  // 关闭表单
  const closeForm = () => {
    setVisible(false);
    if (nodeId === flow.selectedNode?.id) {
      flow.clearNode();
    }
  };

  // 表单值变化
  const handleValuesChange = (changedValues: any, allValues: any) => {
    if (node) {
      // 更新节点数据
      flow.updateNode(node.id, {
        data: allValues
      });
    }
  };

  // 运行调试
  const handleRun = () => {
    // TODO: 实现运行调试逻辑
    console.log('Run node:', node);
  };

  if (!visible || !node) {
    return null;
  }

  return (
    <div
      className="tools-panel"
      style={{
        width,
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        borderRadius: '12px',
        backgroundColor: '#fff',
        position: 'relative',
        boxShadow: '0px 0 10px 1px rgba(16, 24, 40, 0.05)',
        marginLeft: '10px'
      }}
    >
      {/* 头部 */}
      <div
        className="tools-panel__head"
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: '0 15px',
          height: '50px',
          flexShrink: 0,
          borderBottom: '1px solid #f0f0f0'
        }}
      >
        <ToolsIcon name={node?.icon} color={node?.color} />

        <Input
          value={node.label}
          onChange={(e) => {
            flow.updateNode(node.id, { label: e.target.value });
          }}
          style={{ marginLeft: '8px', flex: 1 }}
          bordered={false}
        />

        <div className="btns">
          <Space size="small">
            {flow.isRun(node) && (
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={handleRun}
                className="cl-flow__btn-icon is-bg"
              />
            )}
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={closeForm}
              className="cl-flow__btn-icon is-bg"
            />
          </Space>
        </div>
      </div>

      {/* 描述 */}
      <div style={{ padding: '8px 15px' }}>
        <Input
          value={node.description}
          onChange={(e) => {
            flow.updateNode(node.id, { description: e.target.value });
          }}
          placeholder="节点描述"
          bordered={false}
        />
      </div>

      {/* 表单内容 */}
      <div
        className="tools-panel__container"
        style={{
          position: 'relative',
          flex: 1,
          overflow: 'hidden'
        }}
      >
        <div style={{ height: '100%', overflowY: 'auto', padding: '0 15px' }}>
          <Form
            form={form}
            layout="vertical"
            onValuesChange={handleValuesChange}
            initialValues={node.data}
          >
            {/* TODO: 根据 node.form.items 动态渲染表单项 */}
            {node.form?.items?.map((item, index) => (
              <Form.Item
                key={index}
                label={item.label}
                name={item.prop}
              >
                {/* 这里需要根据 item.component 渲染对应的组件 */}
                <Input placeholder={`请输入${item.label}`} />
              </Form.Item>
            ))}
          </Form>
        </div>
      </div>
    </div>
  );
};

export default ToolsPanelForm;
