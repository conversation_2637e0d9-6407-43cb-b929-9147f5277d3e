import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Timeline, Tag } from 'antd';
import { PlayCircleOutlined, StopOutlined, ClearOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface RunResult {
  id: string;
  nodeId: string;
  nodeName: string;
  status: 'running' | 'success' | 'error' | 'pending';
  duration?: number;
  message?: string;
  timestamp: number;
}

const ToolsPanelRun: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<RunResult[]>([]);

  // 开始运行
  const handleStart = () => {
    setIsRunning(true);
    setResults([]);
    
    // TODO: 实现实际的运行逻辑
    console.log('Start running flow');
    
    // 模拟运行过程
    setTimeout(() => {
      setIsRunning(false);
      setResults([
        {
          id: '1',
          nodeId: 'start',
          nodeName: '开始节点',
          status: 'success',
          duration: 100,
          message: '执行成功',
          timestamp: Date.now()
        },
        {
          id: '2',
          nodeId: 'code',
          nodeName: '代码节点',
          status: 'success',
          duration: 250,
          message: '代码执行完成',
          timestamp: Date.now() + 1000
        }
      ]);
    }, 3000);
  };

  // 停止运行
  const handleStop = () => {
    setIsRunning(false);
    // TODO: 实现停止逻辑
    console.log('Stop running flow');
  };

  // 清空结果
  const handleClear = () => {
    setResults([]);
  };

  const getStatusColor = (status: RunResult['status']) => {
    switch (status) {
      case 'running':
        return 'processing';
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'pending':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: RunResult['status']) => {
    switch (status) {
      case 'running':
        return '运行中';
      case 'success':
        return '成功';
      case 'error':
        return '失败';
      case 'pending':
        return '等待';
      default:
        return '未知';
    }
  };

  return (
    <Card
      title="运行调试"
      size="small"
      style={{
        marginTop: '10px',
        marginLeft: '10px',
        width: '400px',
        maxHeight: '300px'
      }}
      extra={
        <Space size="small">
          {!isRunning ? (
            <Button
              type="primary"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={handleStart}
            >
              运行
            </Button>
          ) : (
            <Button
              danger
              size="small"
              icon={<StopOutlined />}
              onClick={handleStop}
            >
              停止
            </Button>
          )}
          <Button
            size="small"
            icon={<ClearOutlined />}
            onClick={handleClear}
            disabled={isRunning}
          >
            清空
          </Button>
        </Space>
      }
    >
      <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
        {isRunning && results.length === 0 && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Text type="secondary">正在运行中...</Text>
          </div>
        )}

        {results.length > 0 && (
          <Timeline
            size="small"
            items={results.map(result => ({
              color: getStatusColor(result.status),
              children: (
                <div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Text strong>{result.nodeName}</Text>
                    <Tag color={getStatusColor(result.status)} size="small">
                      {getStatusText(result.status)}
                    </Tag>
                    {result.duration && (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {result.duration}ms
                      </Text>
                    )}
                  </div>
                  {result.message && (
                    <Paragraph
                      style={{ margin: '4px 0 0 0', fontSize: '12px' }}
                      type="secondary"
                    >
                      {result.message}
                    </Paragraph>
                  )}
                </div>
              )
            }))}
          />
        )}

        {!isRunning && results.length === 0 && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Text type="secondary">点击运行按钮开始调试</Text>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ToolsPanelRun;
