import React, { useState, useCallback, useMemo } from 'react';
import { Panel, useReactFlow } from '@xyflow/react';
import { Tooltip, Dropdown, Space } from 'antd';
import { 
  ZoomInOutlined,
  ZoomOutOutlined,
  ExpandOutlined,
  DragOutlined,
  HandOutlined,
  HistoryOutlined,
  PlusOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useFlow } from '../../hooks';

const ToolsControls: React.FC = () => {
  const reactFlow = useReactFlow();
  const flow = useFlow();
  const [showMap, setShowMap] = useState(false);

  // 缩放比例
  const zoom = useMemo(() => {
    return Math.ceil(flow.viewport.zoom * 100 || 100);
  }, [flow.viewport.zoom]);

  // 缩放比例列表
  const ratioItems = [
    { key: '1', label: '默认', value: 1 },
    { key: '1.5', label: '大', value: 1.5 },
    { key: '0.5', label: '小', value: 0.5 }
  ];

  // 自适应
  const handleZoomFit = useCallback(() => {
    reactFlow.fitView();
  }, [reactFlow]);

  // 设置缩放比例
  const handleZoomTo = useCallback((value: number) => {
    reactFlow.zoomTo(value);
  }, [reactFlow]);

  // 对齐
  const handleArrange = useCallback(() => {
    flow.arrange();
  }, [flow]);

  // 切换交互模式
  const handleModeToggle = useCallback(() => {
    const newMode = flow.controlMode === 'hand' ? 'pointer' : 'hand';
    flow.setControlMode(newMode);
  }, [flow]);

  // 历史记录
  const handleHistory = useCallback(() => {
    // TODO: 实现历史记录逻辑
    console.log('Show history');
  }, []);

  // 添加节点
  const handleAddNode = useCallback(() => {
    // TODO: 实现添加节点逻辑
    console.log('Add node');
  }, []);

  // 缩略图切换
  const handleMapToggle = useCallback(() => {
    setShowMap(!showMap);
  }, [showMap]);

  const zoomMenuItems = [
    ...ratioItems.map(item => ({
      key: item.key,
      label: item.label,
      onClick: () => handleZoomTo(item.value)
    })),
    {
      key: 'fit',
      label: '自适应',
      onClick: handleZoomFit
    }
  ];

  return (
    <Panel position="bottom-right" className="tools-controls">
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(8px)',
          borderRadius: '8px',
          padding: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}
      >
        {/* 缩放控制 */}
        <Dropdown
          menu={{ items: zoomMenuItems }}
          trigger={['click']}
          placement="topRight"
        >
          <div
            className="zoom cl-flow__btn-icon"
            style={{
              padding: '4px 8px',
              fontSize: '12px',
              cursor: 'pointer'
            }}
          >
            {zoom}%
          </div>
        </Dropdown>

        <div style={{ height: '1px', background: '#f0f0f0', margin: '4px 0' }} />

        {/* 交互模式切换 */}
        <Tooltip title="切换交互模式" placement="left">
          <div className="cl-flow__btn-icon" onClick={handleModeToggle}>
            {flow.controlMode === 'pointer' ? <DragOutlined /> : <HandOutlined />}
          </div>
        </Tooltip>

        {/* 自适应 */}
        <Tooltip title="自适应" placement="left">
          <div className="cl-flow__btn-icon" onClick={handleZoomFit}>
            <ExpandOutlined />
          </div>
        </Tooltip>

        {/* 对齐 */}
        <Tooltip title="整理节点" placement="left">
          <div className="cl-flow__btn-icon" onClick={handleArrange}>
            <AppstoreOutlined />
          </div>
        </Tooltip>

        {/* 历史记录 */}
        <Tooltip title="历史记录" placement="left">
          <div className="cl-flow__btn-icon" onClick={handleHistory}>
            <HistoryOutlined />
          </div>
        </Tooltip>

        {/* 添加节点 */}
        <Tooltip title="添加节点" placement="left">
          <div className="cl-flow__btn-icon" onClick={handleAddNode}>
            <PlusOutlined />
          </div>
        </Tooltip>

        {/* 缩略图 */}
        <Tooltip title="缩略图" placement="left">
          <div 
            className={`cl-flow__btn-icon ${showMap ? 'is-active' : ''}`} 
            onClick={handleMapToggle}
          >
            <ZoomInOutlined />
          </div>
        </Tooltip>
      </div>

      {/* 缩略图组件 */}
      {showMap && (
        <div
          style={{
            position: 'absolute',
            bottom: '100%',
            right: 0,
            marginBottom: '8px',
            width: '200px',
            height: '150px',
            background: '#fff',
            border: '1px solid #f0f0f0',
            borderRadius: '8px',
            overflow: 'hidden'
          }}
        >
          {/* TODO: 实现缩略图组件 */}
          <div style={{ padding: '8px', fontSize: '12px', color: '#666' }}>
            缩略图
          </div>
        </div>
      )}
    </Panel>
  );
};

export default ToolsControls;
