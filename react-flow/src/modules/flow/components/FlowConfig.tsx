import React, { useState, useCallback, useRef } from 'react';
import { Button, Modal, Row, Col, Typography, Space } from 'antd';
import { DeleteOutlined, SettingOutlined, PlusOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface ConfigItem {
  id: string;
  type: string;
  name: string;
  title: string;
  description?: string;
}

interface FlowConfigProps {
  children?: React.ReactNode;
  onSelect?: (item: ConfigItem) => void;
}

const FlowConfig: React.FC<FlowConfigProps> = ({ children, onSelect }) => {
  const [visible, setVisible] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('flow');
  const [list, setList] = useState<ConfigItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<ConfigItem | null>(null);

  // 打开配置对话框
  const open = useCallback(() => {
    setVisible(true);
    // TODO: 加载配置数据
    loadConfigData();
  }, []);

  // 关闭对话框
  const onClose = useCallback(() => {
    setVisible(false);
    setSelectedItem(null);
  }, []);

  // 加载配置数据
  const loadConfigData = useCallback(async () => {
    try {
      // TODO: 实现实际的数据加载逻辑
      const mockData: ConfigItem[] = [
        {
          id: '1',
          type: 'flow',
          name: 'sample-flow',
          title: '示例流程',
          description: '这是一个示例流程配置'
        },
        {
          id: '2',
          type: 'node',
          name: 'sample-node',
          title: '示例节点',
          description: '这是一个示例节点配置'
        }
      ];
      setList(mockData);
    } catch (error) {
      console.error('Failed to load config data:', error);
    }
  }, []);

  // 选择配置项
  const handleSelect = useCallback((item: ConfigItem) => {
    setSelectedItem(item);
    onSelect?.(item);
  }, [onSelect]);

  // 添加配置
  const handleAdd = useCallback(() => {
    // TODO: 实现添加配置逻辑
    console.log('Add config');
  }, []);

  // 编辑配置
  const handleEdit = useCallback((item: ConfigItem) => {
    // TODO: 实现编辑配置逻辑
    console.log('Edit config:', item);
  }, []);

  // 删除配置
  const handleDelete = useCallback((item: ConfigItem) => {
    // TODO: 实现删除配置逻辑
    console.log('Delete config:', item);
    setList(prev => prev.filter(i => i.id !== item.id));
  }, []);

  // 节点类型列表
  const nodeTypes = [
    { type: 'flow', title: '流程' },
    { type: 'node', title: '节点' },
    { type: 'func', title: '函数' }
  ];

  return (
    <>
      {children ? (
        <div onClick={open}>{children}</div>
      ) : (
        <Button onClick={open}>配置</Button>
      )}

      <Modal
        title="配置"
        open={visible}
        onCancel={onClose}
        width={1200}
        height={700}
        footer={null}
        styles={{
          body: { height: '600px', padding: 0 }
        }}
      >
        <div style={{ display: 'flex', height: '100%' }}>
          {/* 左侧类型列表 */}
          <div style={{ width: 200, borderRight: '1px solid #f0f0f0', padding: 16 }}>
            <div style={{ marginBottom: 16, fontWeight: 'bold' }}>节点类型</div>
            {nodeTypes.map(type => (
              <div
                key={type.type}
                className={`node-item ${selectedType === type.type ? 'is-active' : ''}`}
                style={{
                  padding: '8px 12px',
                  cursor: 'pointer',
                  borderRadius: 4,
                  marginBottom: 4,
                  backgroundColor: selectedType === type.type ? '#e6f7ff' : 'transparent'
                }}
                onClick={() => setSelectedType(type.type)}
              >
                <span>{type.title}</span>
              </div>
            ))}
          </div>

          {/* 右侧配置列表 */}
          <div style={{ flex: 1, padding: 16 }}>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ fontWeight: 'bold' }}>配置列表</div>
              <Button 
                type="primary" 
                size="small" 
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                添加
              </Button>
            </div>

            <Row gutter={[16, 16]}>
              {list
                .filter(item => item.type === selectedType)
                .map(item => (
                  <Col span={8} key={item.id}>
                    <div 
                      className="data-item"
                      style={{
                        border: '1px solid #f0f0f0',
                        borderRadius: 8,
                        padding: 16,
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        backgroundColor: selectedItem?.id === item.id ? '#f6ffed' : '#fff'
                      }}
                      onClick={() => handleSelect(item)}
                    >
                      <div 
                        className="head"
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: 8
                        }}
                      >
                        <span className="name" style={{ fontWeight: 'bold' }}>
                          {item.name}
                        </span>
                        <Space>
                          <SettingOutlined 
                            style={{ cursor: 'pointer' }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(item);
                            }}
                          />
                          <DeleteOutlined 
                            style={{ cursor: 'pointer', color: '#ff4d4f' }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(item);
                            }}
                          />
                        </Space>
                      </div>
                      <div className="content">
                        <Text 
                          type="secondary" 
                          style={{ fontSize: 12 }}
                          ellipsis={{ rows: 4 }}
                        >
                          {item.description || '暂无描述'}
                        </Text>
                      </div>
                    </div>
                  </Col>
                ))}
            </Row>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default FlowConfig;
