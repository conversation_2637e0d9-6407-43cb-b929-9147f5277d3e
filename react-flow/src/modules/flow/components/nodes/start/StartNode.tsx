import React from 'react';
import { isEmpty } from 'lodash-es';

// 内联类型定义
interface FlowNode {
  id: string;
  data?: {
    inputParams?: any[];
  };
  [key: string]: any;
}
import ToolsFields from '../../tools/ToolsFields';

interface StartNodeProps {
  node: FlowNode;
  focus?: boolean;
}

const StartNode: React.FC<StartNodeProps> = ({ node, focus = false }) => {
  if (isEmpty(node?.data?.inputParams) || focus) {
    return null;
  }

  return (
    <div className="node-start" style={{ paddingBottom: '15px' }}>
      <ToolsFields 
        value={node.data!.inputParams} 
        disabled 
        onChange={() => {}} 
      />
    </div>
  );
};

export default StartNode;
