import React from 'react';
import { FlowField } from '../../../../types';

interface FormFieldsProps {
  value?: FlowField[];
  onChange?: (value: FlowField[]) => void;
}

const FormFields: React.FC<FormFieldsProps> = ({
  value = [],
  onChange
}) => {
  return (
    <div className="form-fields">
      {/* TODO: 实现字段表单组件 */}
      <div>Fields Form Component</div>
    </div>
  );
};

export default FormFields;
