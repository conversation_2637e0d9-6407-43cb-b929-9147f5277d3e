// 内联类型定义
interface NodeConfig {
  group: string;
  label: string;
  description: string;
  color: string;
  form?: {
    items: any[];
  };
  data?: any;
  handle?: any;
  component?: any;
  validator?: (data: any) => void | string;
}

import StartNodeComponent from './StartNode';
import FormFields from './form/FormFields';
import { isEmpty } from 'lodash-es';

export default (): NodeConfig => {
  return {
    group: '基础',
    label: '开始',
    description: '开始节点',
    color: '#409eff',
    form: {
      items: [
        {
          label: '输入字段',
          prop: 'inputParams',
          component: {
            vm: FormFields
          }
        }
      ]
    },
    data: {
      inputParams: [
        {
          label: '内容',
          field: 'content',
          type: 'text',
          required: true
        }
      ]
    },
    handle: {
      target: false
    },
    component: StartNodeComponent,
    validator(data) {
      if (isEmpty(data.inputParams)) {
        return '至少要输入一个字段';
      }
    }
  };
};
