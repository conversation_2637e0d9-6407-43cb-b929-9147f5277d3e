import { NodeConfig } from '../../types';

// 动态导入所有节点配置
const nodeModules = import.meta.glob('./*/index.ts', { eager: true });

const CustomNodes: NodeConfig[] = [];

for (const path in nodeModules) {
  const module = nodeModules[path] as { default: () => NodeConfig };
  const [, type] = path.split('/');
  
  const nodeConfig = module.default();
  
  if (nodeConfig.enable !== false) {
    const configWidth = nodeConfig.form?.width || '400px';
    const width = `${parseFloat(configWidth) + 30}px`;

    CustomNodes.push({
      ...nodeConfig,
      type,
      name: `node-${type}`,
      icon: type,
      cardWidth: width
    });
  }
}

export { CustomNodes };
