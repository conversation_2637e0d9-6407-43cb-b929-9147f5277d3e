import React from 'react';

// 内联类型定义
interface FlowNode {
  id: string;
  data?: {
    options?: {
      flowId?: number;
    };
  };
  [key: string]: any;
}
import FlowInfo from './form/FlowInfo';

interface FlowNodeProps {
  node: FlowNode;
  focus?: boolean;
}

const FlowNodeComponent: React.FC<FlowNodeProps> = ({ node, focus = false }) => {
  if (!node.data?.options?.flowId || focus) {
    return null;
  }

  return (
    <div className="node-flow" style={{ paddingBottom: '15px' }}>
      <FlowInfo flowId={node.data.options.flowId} />
    </div>
  );
};

export default FlowNodeComponent;
