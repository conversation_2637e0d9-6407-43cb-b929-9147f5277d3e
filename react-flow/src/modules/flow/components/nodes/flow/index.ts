import { NodeConfig } from '../../../types';
import FlowNodeComponent from './FlowNode';
import FormInputParams from '../_base/form/FormInputParams';
import FormOutputParams from '../_base/form/FormOutputParams';
import FormSelect from '../_base/form/FormSelect';

export default (): NodeConfig => {
  return {
    group: '扩展',
    label: '流程',
    description: '执行其他流程',
    color: '#fd9d2f',
    component: FlowNodeComponent,
    form: {
      items: [
        {
          label: '输入变量',
          prop: 'inputParams',
          component: {
            vm: FormInputParams,
            props: {
              disabled: true,
              editField: false,
              placeholder: '请先选择流程'
            }
          }
        },
        {
          label: '选择流程',
          prop: 'options.flowId',
          component: {
            vm: FormSelect
          }
        },
        {
          label: '输出变量',
          prop: 'outputParams',
          component: {
            vm: FormOutputParams,
            props: {
              editField: false,
              editType: false,
              op: false
            }
          }
        }
      ]
    },
    data: {
      options: {},
      inputParams: [],
      outputParams: []
    },
    validator(data) {
      // 验证流程是否选择
      if (!data.options?.flowId) {
        return '请选择流程';
      }
    }
  };
};
