import React, { useState, useEffect } from 'react';
import { Tag, Typography } from 'antd';

const { Text } = Typography;

interface FlowInfoProps {
  flowId: number;
}

interface FlowInfoData {
  id: number;
  name: string;
  label: string;
  description?: string;
}

const FlowInfo: React.FC<FlowInfoProps> = ({ flowId }) => {
  const [info, setInfo] = useState<FlowInfoData | null>(null);

  useEffect(() => {
    if (flowId) {
      // TODO: 实现获取流程信息的逻辑
      // 这里应该调用 API 获取流程信息
      setInfo({
        id: flowId,
        name: `Flow ${flowId}`,
        label: `流程 ${flowId}`,
        description: '流程描述'
      });
    }
  }, [flowId]);

  if (!info) {
    return null;
  }

  return (
    <div className="form-info">
      <Tag color="success" style={{ marginBottom: 8 }}>
        {info.label}
      </Tag>
      <Text ellipsis style={{ display: 'block', fontSize: '12px' }}>
        {info.name}
      </Text>
    </div>
  );
};

export default FlowInfo;
