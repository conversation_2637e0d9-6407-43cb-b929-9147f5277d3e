import React from 'react';
import { Input } from 'antd';

const { TextArea } = Input;

interface CodeEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  language?: string;
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  placeholder = '请输入代码',
  language = 'javascript'
}) => {
  return (
    <div className="code-editor">
      <TextArea
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder={placeholder}
        rows={10}
        style={{ fontFamily: 'monospace' }}
      />
    </div>
  );
};

export default CodeEditor;
