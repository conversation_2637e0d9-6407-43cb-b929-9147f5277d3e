import React from 'react';
import { Select } from 'antd';

interface FormSelectProps {
  value?: any;
  onChange?: (value: any) => void;
  options?: Array<{ label: string; value: any }>;
  placeholder?: string;
}

const FormSelect: React.FC<FormSelectProps> = ({
  value,
  onChange,
  options = [],
  placeholder = '请选择'
}) => {
  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      style={{ width: '100%' }}
      options={options}
    />
  );
};

export default FormSelect;
