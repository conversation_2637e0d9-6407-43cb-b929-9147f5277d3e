import React from 'react';
import { FlowField } from '../../../../types';

interface FormInputParamsProps {
  value?: FlowField[];
  onChange?: (value: FlowField[]) => void;
  disabled?: boolean;
  editField?: boolean;
  placeholder?: string;
}

const FormInputParams: React.FC<FormInputParamsProps> = ({
  value = [],
  onChange,
  disabled = false,
  editField = true,
  placeholder
}) => {
  return (
    <div className="form-input-params">
      {/* TODO: 实现输入参数表单组件 */}
      <div>Input Params Form Component</div>
      {placeholder && <div className="placeholder">{placeholder}</div>}
    </div>
  );
};

export default FormInputParams;
