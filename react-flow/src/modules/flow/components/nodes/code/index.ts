import { NodeConfig } from '../../../types';
import CodeNodeComponent from './CodeNode';
import FormInputParams from '../_base/form/FormInputParams';
import FormOutputParams from '../_base/form/FormOutputParams';
import CodeEditor from '../_base/form/CodeEditor';

export default (): NodeConfig => {
  return {
    group: '行为',
    label: '执行代码',
    description: '执行一段自定义代码，可以调用框架的插件、数据库、service等',
    color: '#67c23a',
    component: CodeNodeComponent,
    form: {
      width: '500px',
      items: [
        {
          label: '输入变量',
          prop: 'inputParams',
          component: {
            vm: FormInputParams
          }
        },
        {
          label: '代码编辑',
          prop: 'options.code',
          component: {
            vm: CodeEditor
          }
        },
        {
          label: '输出变量',
          prop: 'outputParams',
          component: {
            vm: FormOutputParams
          }
        }
      ]
    },
    data: {
      inputParams: [
        {
          field: 'arg1'
        }
      ],
      outputParams: [
        {
          field: 'result',
          type: 'string'
        }
      ],
      options: {
        code: `import axios from 'axios';
import * as _ from 'lodash';
import * as moment from 'moment';

// 输入参数
const { arg1 } = input;

// 输出结果
return {
  result: 'Hello World'
};`
      }
    },
    validator(data) {
      if (!data.options?.code) {
        return '请输入代码';
      }
    }
  };
};
