import React, { useCallback, useEffect, useRef } from 'react';
import {
  ReactFlow,
  Background,
  Connection,
  NodeDragEvent,
  EdgeMouseEvent,
  NodeMouseEvent,
  useReactFlow,
  ReactFlowProvider
} from '@xyflow/react';
import { useFlow } from '../hooks';
import { FlowNode, FlowEdge } from '../types';

// 导入工具组件
import ToolsHead from './tools/ToolsHead';
import ToolsSelection from './tools/ToolsSelection';
import ToolsPanel from './tools/ToolsPanel';
import ToolsContextMenu from './tools/ToolsContextMenu';
import ToolsControls from './tools/ToolsControls';
import ToolsCard from './tools/card/ToolsCard';
import ToolsEdgeButton from './tools/ToolsEdgeButton';

// 导入样式
import '@xyflow/react/dist/style.css';
import './FlowComponent.css';

interface FlowComponentProps {
  flowId?: number;
}

const FlowComponentInner: React.FC<FlowComponentProps> = ({ flowId }) => {
  const flow = useFlow();
  const contextMenuRef = useRef<any>(null);

  // 加载完成
  const onPaneReady = useCallback(() => {
    flow.init();
    if (flowId) {
      flow.get(flowId);
    }
  }, [flow, flowId]);

  // 视图滚动
  const onPaneScroll = useCallback((event?: WheelEvent) => {
    if (!event) return;

    const nodes = document.querySelectorAll('.react-flow__node');
    const { zoom } = flow.viewport;

    nodes.forEach(node => {
      const content = node.querySelector('.node-content');
      if (content) {
        const scale = Math.min(Math.max(zoom, 0.5), 1.5);
        (content as HTMLElement).style.fontSize = `${14 * scale}px`;
      }
    });
  }, [flow.viewport]);

  // 连接
  const onConnect = useCallback((connection: Connection) => {
    flow.addEdge(connection);
  }, [flow]);

  // 节点鼠标进入
  const onNodeMouseEnter = useCallback((event: React.MouseEvent, node: FlowNode) => {
    flow.activeEdge(node.id, true);
  }, [flow]);

  // 节点鼠标离开
  const onNodeMouseLeave = useCallback((event: React.MouseEvent, node: FlowNode) => {
    flow.activeEdge(node.id, false);
  }, [flow]);

  // 节点点击
  const onNodeClick = useCallback((event: React.MouseEvent, node: FlowNode) => {
    flow.setNode(node);
  }, [flow]);

  // 节点拖拽停止
  const onNodeDragStop = useCallback((event: React.MouseEvent, node: FlowNode, nodes: FlowNode[]) => {
    // 处理节点拖拽停止逻辑
  }, []);

  // 节点右键菜单
  const onNodeContextMenu = useCallback((event: React.MouseEvent, node: FlowNode) => {
    event.preventDefault();
    contextMenuRef.current?.onNode({ event, node });
  }, []);

  // 边鼠标进入
  const onEdgeMouseEnter = useCallback((event: React.MouseEvent, edge: FlowEdge) => {
    // 处理边鼠标进入逻辑
  }, []);

  // 边鼠标离开
  const onEdgeMouseLeave = useCallback((event: React.MouseEvent, edge: FlowEdge) => {
    // 处理边鼠标离开逻辑
  }, []);

  // 画布右键菜单
  const onPaneContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    contextMenuRef.current?.onPane(event);
  }, []);

  // 画布点击
  const onPaneClick = useCallback((event: React.MouseEvent) => {
    flow.clearNode();
  }, [flow]);

  // 自定义节点类型
  const nodeTypes = React.useMemo(() => {
    const types: Record<string, React.ComponentType<any>> = {};
    
    flow.CustomNodes.forEach(nodeConfig => {
      if (nodeConfig.type) {
        types[nodeConfig.type] = (props: any) => (
          <ToolsCard nodeId={props.id} />
        );
      }
    });
    
    return types;
  }, [flow.CustomNodes]);

  return (
    <div className="cl-flow" id="cl-flow">
      <ReactFlow
        nodes={flow.nodes}
        edges={flow.edges}
        onNodesChange={flow.onNodesChange}
        onEdgesChange={flow.onEdgesChange}
        onConnect={onConnect}
        onNodeMouseEnter={onNodeMouseEnter}
        onNodeMouseLeave={onNodeMouseLeave}
        onNodeClick={onNodeClick}
        onNodeDragStop={onNodeDragStop}
        onNodeContextMenu={onNodeContextMenu}
        onEdgeMouseEnter={onEdgeMouseEnter}
        onEdgeMouseLeave={onEdgeMouseLeave}
        onPaneContextMenu={onPaneContextMenu}
        onPaneClick={onPaneClick}
        onInit={onPaneReady}
        nodeTypes={nodeTypes}
        defaultViewport={{ zoom: 1.0 }}
        minZoom={0.5}
        maxZoom={1.5}
        fitView
      >
        {/* 自定义顶部栏 */}
        <ToolsHead />

        {/* 自定义选择框 */}
        <ToolsSelection />

        {/* 自定义面板 */}
        <ToolsPanel />

        {/* 自定义右键菜单 */}
        <ToolsContextMenu ref={contextMenuRef} />

        {/* 自定义控制器 */}
        <ToolsControls />

        {/* 背景 */}
        <Background color="#aaa" gap={16} />
      </ReactFlow>
    </div>
  );
};

const FlowComponent: React.FC<FlowComponentProps> = (props) => {
  return (
    <ReactFlowProvider>
      <FlowComponentInner {...props} />
    </ReactFlowProvider>
  );
};

export default FlowComponent;
