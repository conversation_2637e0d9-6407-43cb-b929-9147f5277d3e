import { useState, useCallback, useMemo, useRef } from 'react';
import { 
  useReactFlow, 
  useNodesState, 
  useEdgesState,
  Connection,
  Node,
  Edge,
  Viewport
} from '@xyflow/react';
import { 
  assign,
  cloneDeep,
  debounce,
  groupBy,
  isArray,
  isEmpty,
  keys,
  last,
  orderBy
} from 'lodash-es';
import ELK from 'elkjs';
import { 
  FlowNode, 
  FlowEdge, 
  FlowInfo, 
  ControlMode, 
  ViewportConfig,
  NodeConfig,
  FlowData
} from '../types';
import { CustomNodes } from '../components/nodes';

const elk = new ELK();

const offset = {
  x: 400,
  y_t: -10,
  y_b: 50,
  g: 150
};

let nodeIdCounter = 1;

/**
 * React Flow Hook - 转换自 Vue 版本的 Pinia store
 */
export const useFlow = () => {
  const reactFlow = useReactFlow();
  
  // 节点和连线状态
  const [nodes, setNodes, onNodesChange] = useNodesState<FlowNode>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<FlowEdge>([]);
  
  // 当前选中的节点
  const [selectedNode, setSelectedNode] = useState<FlowNode | undefined>();
  
  // 缩放比例
  const [zoom, setZoom] = useState(100);
  
  // 控制模式
  const [controlMode, setControlMode] = useState<ControlMode>('hand');
  
  // 流程信息
  const [flowInfo, setFlowInfo] = useState<FlowInfo | undefined>();
  
  // 节点配置注册表
  const nodeConfigs = useRef<Map<string, NodeConfig>>(new Map());
  
  // 视图配置
  const viewport = useMemo(() => {
    return reactFlow.getViewport();
  }, [reactFlow]);

  // 选中节点
  const setNode = useCallback((data: FlowNode | undefined) => {
    setSelectedNode(data);
  }, []);

  // 清空选择节点
  const clearNode = useCallback((isUpdate: boolean = true) => {
    if (isUpdate && selectedNode) {
      updateChildrenPosition('close', selectedNode, selectedNode.id!);
    }
    setSelectedNode(undefined);
  }, [selectedNode]);

  // 查找节点
  const findNode = useCallback((id: string): FlowNode | undefined => {
    return nodes.find(n => n.id === id) as FlowNode;
  }, [nodes]);

  // 根据类型查找节点
  const findNodeByType = useCallback((type: string): FlowNode | undefined => {
    return nodes.find(n => n.type === type) as FlowNode;
  }, [nodes]);

  // 添加节点
  const addNode = useCallback((type: string, options?: Partial<FlowNode>): FlowNode | undefined => {
    const nodeConfig = CustomNodes.find(e => e.type === type);
    
    if (!nodeConfig) {
      console.warn(`Node type "${type}" not found in CustomNodes`);
      return undefined;
    }

    const newNode: FlowNode = {
      id: String(nodeIdCounter++),
      type,
      position: { x: 0, y: 0 },
      data: {
        inputParams: [],
        outputParams: [],
        options: {}
      },
      ...nodeConfig,
      ...options,
      _index: 1
    };

    // 判断是否有重复
    const same = orderBy(nodes, '_index', 'desc').find(e => e.type === type);
    if (same) {
      newNode._index = (same._index || 1) + 1;
      newNode.label = `${newNode.label!} ${newNode._index!}`;
    }

    const clonedNode = cloneDeep(newNode);
    setNodes(nds => [...nds, clonedNode]);

    return findNode(clonedNode.id);
  }, [nodes, setNodes, findNode]);

  // 插入节点，自动计算位置
  const insertNode = useCallback((type: string, source: FlowNode, options?: Partial<FlowNode>): FlowNode | undefined => {
    if (source) {
      return addNode(type, {
        position: calcPosition(source, undefined),
        ...options
      });
    }
    return undefined;
  }, [addNode]);

  // 更新节点
  const updateNode = useCallback((id: string, data: Partial<FlowNode>) => {
    const node = findNode(id);
    
    if (node) {
      // 不同类型，清空相关连线
      if (data.name && data.name !== node.name) {
        removeEdgeByNodeId(id);
        
        ['handle', 'data', 'form'].forEach(k => {
          if (!data[k as keyof FlowNode]) {
            (data as any)[k] = {};
          }
        });
      }

      const updatedNode = { ...node, ...data };
      setNodes(nds => nds.map(n => n.id === id ? updatedNode : n));
    }
  }, [findNode, setNodes]);

  // 移除节点
  const removeNodes = useCallback((nodesToRemove: FlowNode[] | FlowNode, force?: boolean) => {
    const vals = (isArray(nodesToRemove) ? nodesToRemove : [nodesToRemove]).filter(e =>
      force ? true : e.type !== 'start'
    );

    // 移除节点
    setNodes(nds => nds.filter(n => !vals.some(v => v.id === n.id)));

    // 移除连线
    vals.forEach(e => {
      removeEdgeByNodeId(e.id);

      // 如果是当前选中节点，则清空
      if (e.id === selectedNode?.id) {
        clearNode();
      }
    });
  }, [setNodes, selectedNode, clearNode]);

  // 父节点
  const parentNodes = useCallback((id: string): FlowNode[] => {
    return edges.filter(e => e.target === id).map(e => findNode(e.source!)).filter(Boolean) as FlowNode[];
  }, [edges, findNode]);

  // 所有父节点
  const parentAllNodes = useCallback((id: string): FlowNode[] => {
    const nodes: FlowNode[] = [];
    const nodeIds = new Set<string>();

    function next(id: string) {
      const arr = parentNodes(id);

      if (!isEmpty(arr)) {
        arr.forEach(e => {
          if (!nodeIds.has(e.id!)) {
            nodeIds.add(e.id!);
            nodes.push(e);
            next(e.id!);
          }
        });
      }
    }

    next(id);
    return orderBy(nodes, 'id', 'asc');
  }, [parentNodes]);

  // 子节点
  const childrenNodes = useCallback((id: string, handle?: string): FlowNode[] => {
    const node = findNode(id);
    
    // 下级连接线
    const childrenEdges = edges.filter(e => e.source === id);
    
    // 下级连接点
    const handles = node?.handle?.next || [{ value: 'source' }];
    
    // 根据连接点顺序返回
    return handles
      .filter(e => handle ? e.value === handle : true)
      .map((a: { value: string }) => {
        const edge = childrenEdges.find(e => e.sourceHandle === a.value);
        return edge ? findNode(edge.target!) : null;
      })
      .filter(Boolean) as FlowNode[];
  }, [findNode, edges]);

  // 子所有节点
  const childrenAllNodes = useCallback((id: string, handle?: string): FlowNode[] => {
    const nodes: FlowNode[] = [];

    function next(id: string, i = 0) {
      const children = childrenNodes(id, i === 0 ? handle : undefined);

      children.forEach(e => {
        nodes.push(e);
        next(e.id!, i + 1);
      });
    }

    next(id, 0);
    return nodes;
  }, [childrenNodes]);

  // 叶子节点
  const leafNodes = useCallback((id: string): FlowNode[] => {
    return childrenAllNodes(id).filter(e => isEmpty(childrenNodes(e.id!)));
  }, [childrenAllNodes, childrenNodes]);

  // 获取与节点所有连接的其他节点
  const getConnectedNodes = useCallback((nodeId: string): FlowNode[] => {
    const pNodes = parentAllNodes(nodeId);
    const cNodes = childrenAllNodes(nodeId);
    const currentNode = findNode(nodeId);

    return [...pNodes, ...(currentNode ? [currentNode] : []), ...cNodes].filter(Boolean);
  }, [parentAllNodes, childrenAllNodes, findNode]);

  // 计算位置信息
  const calcPosition = useCallback((source: FlowNode, target?: FlowNode) => {
    const { x = 0, y = 0 } = source.position || {};

    const position = {
      x: x + offset.x,
      y: 0
    };

    // 子节点
    const children = childrenNodes(source.id!);

    // 第一个子节点
    const firstNode = children[0];

    // 【是否有相邻节点】有目标节点则判断是否与第一个相同，否则判断节点长度
    if (target ? firstNode?.id !== target.id : !isEmpty(children)) {
      // 计算到第几个子节点
      const end = target ? children.findIndex(e => e.id === target?.id) : children.length;

      // 前几个节点高度之和，从第一个节点的 y 开始算
      const height = children
        .filter((_, i) => i < end)
        .reduce((a, b) => {
          const nodeEl = document.querySelector(`div[data-id="${b.id}"]`);
          return a + (nodeEl?.clientHeight || 0) + offset.y_b;
        }, firstNode?.position?.y || 0);

      position.y = height + offset.y_t;
    } else {
      position.y = y;
    }

    return position;
  }, [childrenNodes]);

  // 更新子节点位置的占位函数
  const updateChildrenPosition = useCallback((action: string, node: FlowNode, nodeId: string) => {
    // TODO: 实现子节点位置更新逻辑
    console.log('updateChildrenPosition', action, node, nodeId);
  }, []);

  // 是否存在边
  const hasEdge = useCallback((connection: Connection): FlowEdge[] => {
    return edges.filter(e => {
      if (e.source === connection.source) {
        if (connection.sourceHandle === e.sourceHandle) {
          return true;
        }
      }

      if (e.target === connection.target) {
        if (connection.targetHandle === e.targetHandle) {
          return true;
        }
      }

      return false;
    });
  }, [edges]);

  // 添加边线
  const addEdge = useCallback((connection: Connection) => {
    const existingEdges = hasEdge(connection);

    // 是否只允许一条线
    if (!isEmpty(existingEdges)) {
      // removeEdges(existingEdges);
    }

    const newEdge: FlowEdge = {
      ...connection,
      id: `${connection.source}-${connection.target}`,
      animated: false,
      style: {
        strokeWidth: 1.5
      },
      type: 'button'
    };

    setEdges(eds => [...eds, newEdge]);
  }, [hasEdge, setEdges]);

  // 查找边线
  const findEdge = useCallback((id: string): FlowEdge | undefined => {
    return edges.find(e => e.id === id);
  }, [edges]);

  // 更新边线
  const updateEdge = useCallback((id: string, data: Partial<FlowEdge>) => {
    setEdges(eds => eds.map(e => e.id === id ? { ...e, ...data } : e));
  }, [setEdges]);

  // 移除边线
  const removeEdges = useCallback((edgesToRemove: FlowEdge[] | FlowEdge) => {
    const edgeIds = (isArray(edgesToRemove) ? edgesToRemove : [edgesToRemove]).map(e => e.id);
    setEdges(eds => eds.filter(e => !edgeIds.includes(e.id)));
  }, [setEdges]);

  // 根据节点移除边线
  const removeEdgeByNodeId = useCallback((nodeId: string, type?: 'source' | 'target'): FlowEdge[] => {
    const edgesToRemove = edges.filter(e => {
      if (type) {
        return e[type] === nodeId;
      }
      return e.source === nodeId || e.target === nodeId;
    });

    removeEdges(edgesToRemove);
    return edgesToRemove;
  }, [edges, removeEdges]);

  // 突出已连接的线
  const activeEdge = useCallback((nodeId: string, isShow: boolean) => {
    const primaryColor = getComputedStyle(document.documentElement).getPropertyValue(
      '--el-color-primary'
    ) || '#409eff';

    edges
      .filter(e => e.target === nodeId || e.source === nodeId)
      .forEach(e => {
        const stroke = isShow ? primaryColor : (e as any)._stroke || '';

        // 记录之前的颜色
        (e as any)._stroke = e.style?.stroke;

        updateEdge(e.id, {
          style: {
            ...e.style,
            stroke
          }
        });
      });
  }, [edges, updateEdge]);

  // 能否调试
  const isRun = useCallback((node: FlowNode): boolean => {
    return node ? !['start', 'end'].includes(node.type!) : false;
  }, []);

  // 设置缩放
  const setZoomValue = useCallback((val: number) => {
    setZoom(val);
    reactFlow.setViewport({ ...viewport, zoom: val / 100 });
  }, [setZoom, reactFlow, viewport]);

  // 设置控制模式
  const setControlModeValue = useCallback((val: ControlMode) => {
    setControlMode(val);
    // React Flow 中通过 panOnDrag 属性控制
  }, [setControlMode]);

  // 设置视图
  const setViewport = useCallback((
    { x, y, zoom: zoomValue }: { x: number; y: number; zoom?: number },
    duration = 300
  ) => {
    reactFlow.setViewport(
      { x, y, zoom: zoomValue || viewport.zoom },
      { duration }
    );
  }, [reactFlow, viewport]);

  // 设置节点中心位置
  const setViewportByNode = useCallback(async (node: FlowNode) => {
    if (!node) {
      return false;
    }

    await new Promise(resolve => setTimeout(resolve, 10));

    const { zoom: currentZoom } = viewport;

    if (node) {
      const flowEl = document.querySelector('.cl-flow');
      const panelEl = document.querySelector('.cl-flow .tools-panel-right');
      const nodeEl = document.querySelector(`div[data-id="${node.id}"]`);

      const { x = 0, y = 0 } = node.position || {};

      const top =
        ((flowEl?.clientHeight || 0) - (nodeEl?.clientHeight || 0) * currentZoom) / 2 -
        y * currentZoom;
      const left =
        ((flowEl?.clientWidth || 0) -
          ((panelEl?.clientWidth || 0) + 10) -
          (nodeEl?.clientWidth || 0) * currentZoom) /
          2 -
        x * currentZoom;

      setViewport({ x: left, y: top });
    } else {
      setViewport({ x: 0, y: 0 });
    }
  }, [viewport, setViewport]);

  const setViewportByNodeId = useCallback((nodeId: string) => {
    const node = findNode(nodeId);
    if (node) {
      setViewportByNode(node);
    }
  }, [findNode, setViewportByNode]);

  // 默认初始化
  const initDefault = useCallback(() => {
    if (isEmpty(nodes)) {
      const startNode = addNode('start', {
        position: {
          x: 100,
          y: 100
        }
      });

      addNode('end', {
        position: {
          x: offset.x + offset.g,
          y: 100
        }
      });

      if (startNode) {
        setNode(startNode);
      }
    }
  }, [nodes, addNode, setNode]);

  // 清空画布
  const clear = useCallback(() => {
    // 清空边线
    setEdges([]);
    // 清空节点
    setNodes([]);

    // 默认初始化
    initDefault();

    // 设置视图
    setViewport({ x: 0, y: 0 });
  }, [setEdges, setNodes, initDefault, setViewport]);

  // 复制节点
  const copyNode = useCallback((node: FlowNode) => {
    const copyData = {
      type: 'copyNode',
      data: {
        type: node.type,
        label: node.label,
        description: node.description,
        data: node.data
      }
    };

    navigator.clipboard.writeText(JSON.stringify(copyData));
  }, []);

  // 获取剪贴板节点
  const getClipboardNode = useCallback(async (): Promise<FlowNode | null> => {
    try {
      const text = await navigator.clipboard.readText();
      const { type, data } = JSON.parse(text);

      if (type === 'copyNode') {
        // 清空剪贴板
        navigator.clipboard.writeText('');
        return data;
      }
    } catch (err) {
      console.warn('Failed to read clipboard:', err);
    }

    return null;
  }, []);

  // 自动排列
  const arrange = useCallback(async () => {
    if (isEmpty(nodes)) return;

    try {
      const elkNodes = nodes.map(node => ({
        id: node.id,
        width: 200,
        height: 100
      }));

      const elkEdges = edges.map(edge => ({
        id: edge.id,
        sources: [edge.source],
        targets: [edge.target]
      }));

      const graph = await elk.layout({
        id: 'root',
        layoutOptions: {
          'elk.algorithm': 'layered',
          'elk.direction': 'RIGHT',
          'elk.spacing.nodeNode': '50',
          'elk.layered.spacing.nodeNodeBetweenLayers': '100'
        },
        children: elkNodes,
        edges: elkEdges
      });

      if (graph.children) {
        const updatedNodes = nodes.map(node => {
          const elkNode = graph.children!.find(n => n.id === node.id);
          if (elkNode) {
            return {
              ...node,
              position: {
                x: elkNode.x || 0,
                y: elkNode.y || 0
              }
            };
          }
          return node;
        });

        setNodes(updatedNodes);
      }
    } catch (error) {
      console.error('Auto layout failed:', error);
    }
  }, [nodes, edges, setNodes]);

  // 保存流程
  const save = useCallback(async () => {
    // TODO: 实现保存逻辑
    console.log('Saving flow...', { nodes, edges, flowInfo });
  }, [nodes, edges, flowInfo]);

  // 获取流程数据
  const get = useCallback(async (flowId: number) => {
    // TODO: 实现获取流程数据逻辑
    console.log('Getting flow data for ID:', flowId);
  }, []);

  // 初始化
  const init = useCallback(() => {
    initDefault();
  }, [initDefault]);

  return {
    // 状态
    nodes,
    edges,
    selectedNode,
    zoom,
    controlMode,
    flowInfo,
    viewport,

    // 节点操作
    setNode,
    clearNode,
    findNode,
    findNodeByType,
    addNode,
    insertNode,
    updateNode,
    removeNodes,

    // 节点关系
    parentNodes,
    parentAllNodes,
    childrenNodes,
    childrenAllNodes,
    leafNodes,
    getConnectedNodes,

    // 边操作
    hasEdge,
    addEdge,
    findEdge,
    updateEdge,
    removeEdges,
    removeEdgeByNodeId,
    activeEdge,

    // 视图控制
    setZoom: setZoomValue,
    setControlMode: setControlModeValue,
    setViewport,
    setViewportByNode,
    setViewportByNodeId,

    // 流程操作
    clear,
    copyNode,
    getClipboardNode,
    arrange,
    save,
    get,
    init,

    // 工具函数
    calcPosition,
    updateChildrenPosition,
    isRun,

    // React Flow 原生方法
    onNodesChange,
    onEdgesChange,
    setNodes,
    setEdges,

    // 配置
    CustomNodes,
    nodeConfigs: nodeConfigs.current,

    // 流程信息
    setFlowInfo
  };
};
