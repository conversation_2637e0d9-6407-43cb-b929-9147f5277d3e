import { useState, useCallback } from 'react';

interface SelectionState {
  selectedNodes: string[];
  selectedEdges: string[];
  isSelecting: boolean;
  selectionBox: {
    startX: number;
    startY: number;
    endX: number;
    endY: number;
  } | null;
}

interface UseSelectionReturn extends SelectionState {
  selectNode: (nodeId: string, multi?: boolean) => void;
  selectEdge: (edgeId: string, multi?: boolean) => void;
  selectAll: (nodes: any[], edges: any[]) => void;
  clearSelection: () => void;
  startSelection: (x: number, y: number) => void;
  updateSelection: (x: number, y: number) => void;
  endSelection: () => void;
  isNodeSelected: (nodeId: string) => boolean;
  isEdgeSelected: (edgeId: string) => boolean;
}

export const useSelection = (): UseSelectionReturn => {
  const [state, setState] = useState<SelectionState>({
    selectedNodes: [],
    selectedEdges: [],
    isSelecting: false,
    selectionBox: null
  });

  const selectNode = useCallback((nodeId: string, multi = false) => {
    setState(prev => ({
      ...prev,
      selectedNodes: multi 
        ? prev.selectedNodes.includes(nodeId)
          ? prev.selectedNodes.filter(id => id !== nodeId)
          : [...prev.selectedNodes, nodeId]
        : [nodeId],
      selectedEdges: multi ? prev.selectedEdges : []
    }));
  }, []);

  const selectEdge = useCallback((edgeId: string, multi = false) => {
    setState(prev => ({
      ...prev,
      selectedEdges: multi 
        ? prev.selectedEdges.includes(edgeId)
          ? prev.selectedEdges.filter(id => id !== edgeId)
          : [...prev.selectedEdges, edgeId]
        : [edgeId],
      selectedNodes: multi ? prev.selectedNodes : []
    }));
  }, []);

  const selectAll = useCallback((nodes: any[], edges: any[]) => {
    setState(prev => ({
      ...prev,
      selectedNodes: nodes.map(node => node.id),
      selectedEdges: edges.map(edge => edge.id)
    }));
  }, []);

  const clearSelection = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedNodes: [],
      selectedEdges: []
    }));
  }, []);

  const startSelection = useCallback((x: number, y: number) => {
    setState(prev => ({
      ...prev,
      isSelecting: true,
      selectionBox: {
        startX: x,
        startY: y,
        endX: x,
        endY: y
      }
    }));
  }, []);

  const updateSelection = useCallback((x: number, y: number) => {
    setState(prev => ({
      ...prev,
      selectionBox: prev.selectionBox ? {
        ...prev.selectionBox,
        endX: x,
        endY: y
      } : null
    }));
  }, []);

  const endSelection = useCallback(() => {
    setState(prev => ({
      ...prev,
      isSelecting: false,
      selectionBox: null
    }));
  }, []);

  const isNodeSelected = useCallback((nodeId: string) => {
    return state.selectedNodes.includes(nodeId);
  }, [state.selectedNodes]);

  const isEdgeSelected = useCallback((edgeId: string) => {
    return state.selectedEdges.includes(edgeId);
  }, [state.selectedEdges]);

  return {
    ...state,
    selectNode,
    selectEdge,
    selectAll,
    clearSelection,
    startSelection,
    updateSelection,
    endSelection,
    isNodeSelected,
    isEdgeSelected
  };
};
