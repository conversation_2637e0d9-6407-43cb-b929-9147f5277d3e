import { useEffect } from 'react';

interface KeyboardShortcuts {
  onSave?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onCopy?: () => void;
  onPaste?: () => void;
  onDelete?: () => void;
  onSelectAll?: () => void;
}

export const useKeyboard = (shortcuts: KeyboardShortcuts) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const { ctrlKey, metaKey, key, shiftKey } = event;
      const isCtrlOrCmd = ctrlKey || metaKey;

      // 阻止默认行为的快捷键
      const shouldPreventDefault = [
        'KeyS', 'KeyZ', 'KeyY', 'KeyC', 'KeyV', 'KeyA'
      ].includes(event.code) && isCtrlOrCmd;

      if (shouldPreventDefault) {
        event.preventDefault();
      }

      // 处理快捷键
      if (isCtrlOrCmd) {
        switch (key.toLowerCase()) {
          case 's':
            shortcuts.onSave?.();
            break;
          case 'z':
            if (shiftKey) {
              shortcuts.onRedo?.();
            } else {
              shortcuts.onUndo?.();
            }
            break;
          case 'y':
            shortcuts.onRedo?.();
            break;
          case 'c':
            shortcuts.onCopy?.();
            break;
          case 'v':
            shortcuts.onPaste?.();
            break;
          case 'a':
            shortcuts.onSelectAll?.();
            break;
        }
      } else if (key === 'Delete' || key === 'Backspace') {
        shortcuts.onDelete?.();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [shortcuts]);
};
