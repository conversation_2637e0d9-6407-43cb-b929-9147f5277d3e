import { useCallback, useState, useRef, useMemo } from 'react';
import { 
  useNodesState, 
  useEdgesState, 
  addEdge as reactFlowAddEdge,
  useReactFlow,
  type Connection,
  type Viewport
} from '@xyflow/react';
import { cloneDeep, assign, orderBy, isEmpty, isArray, groupBy, keys } from 'lodash-es';
import { message } from 'antd';
import type { Node, Edge, Connection } from '@xyflow/react';

// 本地类型定义，避免导入问题
interface FlowField {
  field: string;
  type?: string;
  value?: string;
  name?: string;
  nodeId?: string;
}

interface FlowData {
  inputParams?: FlowField[];
  outputParams?: FlowField[];
  options?: any;
}

interface FlowNode extends Node {
  enable?: boolean;
  label?: string;
  description?: string;
  icon?: string;
  name?: `node-${string}`;
  data: FlowData;
}

interface FlowEdge extends Edge {
  animated?: boolean;
  _stroke?: string;
  [key: string]: any;
}

interface FlowConnection {
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

interface LayoutOffset {
  x: number;
  y_t: number;
  y_b: number;
  g: number;
}

interface FlowInfo {
  id?: number;
  name?: string;
  description?: string;
  draft?: {
    nodes: FlowNode[];
    edges: FlowEdge[];
  };
}

interface NodeConfig {
  type: string;
  label: string;
  description?: string;
  icon?: string;
  color?: string;
  defaultData?: FlowData;
}

interface NodeGroup {
  label: string;
  children: NodeConfig[];
}

type ControlMode = 'pointer' | 'hand';

// 布局偏移配置
const offset: LayoutOffset = {
  x: 400,
  y_t: -10,
  y_b: 50,
  g: 150
};

let nodeIdCounter = 1;

/**
 * 核心 Flow Hook
 * 转换自 Vue 版本的 Pinia store
 */
export const useFlow = () => {
  const reactFlow = useReactFlow();
  
  // 节点和连线状态
  const [nodes, setNodes, onNodesChange] = useNodesState<FlowNode>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<FlowEdge>([]);
  
  // 当前选中的节点
  const [selectedNode, setSelectedNode] = useState<FlowNode | undefined>();
  
  // 缩放比例
  const [zoom, setZoom] = useState(100);
  
  // 控制模式
  const [controlMode, setControlMode] = useState<ControlMode>('hand');
  
  // 流程信息
  const [flowInfo, setFlowInfo] = useState<FlowInfo | undefined>();
  
  // 节点配置注册表
  const nodeConfigs = useRef<Map<string, NodeConfig>>(new Map());
  
  // 视图配置
  const viewport = useMemo(() => {
    return reactFlow.getViewport();
  }, [reactFlow]);

  /**
   * 注册节点类型
   */
  const registerNodeType = useCallback((config: NodeConfig) => {
    nodeConfigs.current.set(config.type, config);
  }, []);

  /**
   * 获取所有注册的节点配置
   */
  const getNodeConfigs = useCallback(() => {
    return Array.from(nodeConfigs.current.values());
  }, []);

  /**
   * 根据类型获取节点配置
   */
  const getNodeConfig = useCallback((type: string) => {
    return nodeConfigs.current.get(type);
  }, []);

  /**
   * 查找节点
   */
  const findNode = useCallback((id: string): FlowNode | undefined => {
    return nodes.find(n => n.id === id);
  }, [nodes]);

  /**
   * 根据类型查找节点
   */
  const findNodeByType = useCallback((type: string): FlowNode | undefined => {
    return nodes.find(n => n.type === type);
  }, [nodes]);

  /**
   * 添加节点
   */
  const addNode = useCallback((type: string, options?: Partial<FlowNode>): FlowNode | undefined => {
    const config = getNodeConfig(type);
    if (!config) {
      console.warn(`Node type "${type}" not registered`);
      return;
    }

    const nodeData: FlowNode = {
      id: String(nodeIdCounter++),
      type,
      position: { x: 0, y: 0 },
      data: {
        inputParams: [],
        outputParams: [],
        options: {}
      },
      ...config,
      ...options,
      _index: 1
    };

    // 处理重复节点的命名
    const sameTypeNodes = orderBy(nodes.filter(n => n.type === type), '_index', 'desc');
    if (sameTypeNodes.length > 0) {
      const lastNode = sameTypeNodes[0];
      nodeData._index = (lastNode._index || 1) + 1;
      nodeData.label = `${config.label} ${nodeData._index}`;
    }

    const newNodes = [...nodes, cloneDeep(nodeData)];
    setNodes(newNodes);

    return findNode(nodeData.id);
  }, [nodes, setNodes, getNodeConfig, findNode]);

  /**
   * 更新节点
   */
  const updateNode = useCallback((id: string, data: Partial<FlowNode>) => {
    const node = findNode(id);
    if (!node) return;

    // 如果改变了节点类型，清空相关连线
    if (data.type && data.type !== node.type) {
      removeEdgesByNodeId(id);
    }

    const updatedNodes = nodes.map(n => 
      n.id === id ? assign(cloneDeep(n), data) : n
    );
    setNodes(updatedNodes);
  }, [nodes, setNodes, findNode]);

  /**
   * 移除节点
   */
  const removeNodes = useCallback((nodesToRemove: FlowNode[] | FlowNode, force?: boolean) => {
    const nodeList = isArray(nodesToRemove) ? nodesToRemove : [nodesToRemove];
    
    // start 节点不能删除，除非强制删除
    const validNodes = nodeList.filter(node => 
      force ? true : node.type !== 'start'
    );

    // 移除节点
    const remainingNodes = nodes.filter(node => 
      !validNodes.some(removeNode => removeNode.id === node.id)
    );
    setNodes(remainingNodes);

    // 移除相关连线
    validNodes.forEach(node => {
      removeEdgesByNodeId(node.id);
      
      // 如果是当前选中节点，清空选择
      if (node.id === selectedNode?.id) {
        setSelectedNode(undefined);
      }
    });
  }, [nodes, setNodes, selectedNode]);

  /**
   * 查找连线
   */
  const findEdge = useCallback((id: string): FlowEdge | undefined => {
    return edges.find(e => e.id === id);
  }, [edges]);

  /**
   * 检查是否存在连线
   */
  const hasEdge = useCallback((connection: FlowConnection): FlowEdge[] => {
    return edges.filter(edge => {
      if (edge.source === connection.source && edge.sourceHandle === connection.sourceHandle) {
        return true;
      }
      if (edge.target === connection.target && edge.targetHandle === connection.targetHandle) {
        return true;
      }
      return false;
    });
  }, [edges]);

  /**
   * 添加连线
   */
  const addEdge = useCallback((connection: Connection) => {
    const existingEdges = hasEdge(connection);
    
    // 如果已存在相同连线，可以选择是否替换
    if (!isEmpty(existingEdges)) {
      // 这里可以添加替换逻辑
    }

    const newEdge: FlowEdge = {
      ...connection,
      id: `edge-${connection.source}-${connection.target}-${Date.now()}`,
      animated: false,
      style: {
        strokeWidth: 1.5
      },
      type: 'default'
    };

    setEdges(eds => reactFlowAddEdge(newEdge, eds));
  }, [setEdges, hasEdge]);

  /**
   * 更新连线
   */
  const updateEdge = useCallback((id: string, data: Partial<FlowEdge>) => {
    const updatedEdges = edges.map(edge => 
      edge.id === id ? assign(cloneDeep(edge), data) : edge
    );
    setEdges(updatedEdges);
  }, [edges, setEdges]);

  /**
   * 移除连线
   */
  const removeEdges = useCallback((edgesToRemove: FlowEdge[] | FlowEdge) => {
    const edgeList = isArray(edgesToRemove) ? edgesToRemove : [edgesToRemove];
    const edgeIds = edgeList.map(edge => edge.id);
    
    const remainingEdges = edges.filter(edge => !edgeIds.includes(edge.id));
    setEdges(remainingEdges);
  }, [edges, setEdges]);

  /**
   * 根据节点ID移除连线
   */
  const removeEdgesByNodeId = useCallback((nodeId: string, type?: 'source' | 'target'): FlowEdge[] => {
    const edgesToRemove = edges.filter(edge => {
      if (type === 'source') return edge.source === nodeId;
      if (type === 'target') return edge.target === nodeId;
      return edge.source === nodeId || edge.target === nodeId;
    });

    removeEdges(edgesToRemove);
    return edgesToRemove;
  }, [edges, removeEdges]);

  /**
   * 获取父节点
   */
  const getParentNodes = useCallback((nodeId: string): FlowNode[] => {
    return edges
      .filter(edge => edge.target === nodeId)
      .map(edge => findNode(edge.source))
      .filter(Boolean) as FlowNode[];
  }, [edges, findNode]);

  /**
   * 获取子节点
   */
  const getChildrenNodes = useCallback((nodeId: string, handle?: string): FlowNode[] => {
    const node = findNode(nodeId);
    if (!node) return [];

    const childrenEdges = edges.filter(edge => edge.source === nodeId);
    const handles = node.handle?.next || [{ value: 'source' }];

    return handles
      .filter(h => handle ? h.value === handle : true)
      .map(h => {
        const edge = childrenEdges.find(e => e.sourceHandle === h.value);
        return edge ? findNode(edge.target) : null;
      })
      .filter(Boolean) as FlowNode[];
  }, [edges, findNode]);

  /**
   * 设置视图
   */
  const setViewport = useCallback((viewport: Partial<Viewport>, options?: { duration?: number }) => {
    reactFlow.setViewport(viewport as Viewport, options);
  }, [reactFlow]);

  /**
   * 清空画布
   */
  const clear = useCallback(() => {
    setEdges([]);
    setNodes([]);
    setSelectedNode(undefined);
    setViewport({ x: 0, y: 0, zoom: 1 });
  }, [setEdges, setNodes, setViewport]);

  /**
   * 初始化默认节点
   */
  const initDefault = useCallback(() => {
    if (isEmpty(nodes)) {
      const startNode = addNode('start', {
        position: { x: 100, y: 100 }
      });

      addNode('end', {
        position: { x: offset.x + offset.g, y: 100 }
      });

      if (startNode) {
        setSelectedNode(startNode);
      }
    }
  }, [nodes, addNode]);

  return {
    // 状态
    nodes,
    edges,
    selectedNode,
    zoom,
    controlMode,
    flowInfo,
    viewport,
    
    // 节点操作
    findNode,
    findNodeByType,
    addNode,
    updateNode,
    removeNodes,
    setSelectedNode,
    
    // 连线操作
    findEdge,
    hasEdge,
    addEdge,
    updateEdge,
    removeEdges,
    removeEdgesByNodeId,
    
    // 关系查询
    getParentNodes,
    getChildrenNodes,
    
    // 节点配置
    registerNodeType,
    getNodeConfigs,
    getNodeConfig,
    
    // 视图控制
    setViewport,
    setZoom,
    setControlMode,
    
    // 画布操作
    clear,
    initDefault,
    
    // React Flow 原生方法
    onNodesChange,
    onEdgesChange,
    
    // 常量
    offset
  };
};
