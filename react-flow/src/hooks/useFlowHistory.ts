import { useState, useCallback, useRef } from 'react';

interface FlowData {
  nodes: any[];
  edges: any[];
}

interface UseFlowHistoryReturn {
  flowData: FlowData;
  canUndo: boolean;
  canRedo: boolean;
  updateFlow: (data: FlowData) => void;
  undo: () => void;
  redo: () => void;
  clearHistory: () => void;
}

export const useFlowHistory = (initialData: FlowData): UseFlowHistoryReturn => {
  const [flowData, setFlowData] = useState<FlowData>(initialData);
  const [currentIndex, setCurrentIndex] = useState(0);
  const historyRef = useRef<FlowData[]>([initialData]);
  const isUpdatingRef = useRef(false);

  const updateFlow = useCallback((data: FlowData) => {
    if (isUpdatingRef.current) return;
    
    const newHistory = historyRef.current.slice(0, currentIndex + 1);
    newHistory.push(JSON.parse(JSON.stringify(data))); // 深拷贝
    
    // 限制历史记录数量，避免内存泄漏
    if (newHistory.length > 50) {
      newHistory.shift();
    } else {
      setCurrentIndex(prev => prev + 1);
    }
    
    historyRef.current = newHistory;
    setFlowData(data);
  }, [currentIndex]);

  const undo = useCallback(() => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      const prevData = historyRef.current[newIndex];
      
      isUpdatingRef.current = true;
      setCurrentIndex(newIndex);
      setFlowData(prevData);
      
      // 延迟重置标志，确保不会触发 updateFlow
      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 0);
    }
  }, [currentIndex]);

  const redo = useCallback(() => {
    if (currentIndex < historyRef.current.length - 1) {
      const newIndex = currentIndex + 1;
      const nextData = historyRef.current[newIndex];
      
      isUpdatingRef.current = true;
      setCurrentIndex(newIndex);
      setFlowData(nextData);
      
      // 延迟重置标志，确保不会触发 updateFlow
      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 0);
    }
  }, [currentIndex]);

  const clearHistory = useCallback(() => {
    historyRef.current = [flowData];
    setCurrentIndex(0);
  }, [flowData]);

  const canUndo = currentIndex > 0;
  const canRedo = currentIndex < historyRef.current.length - 1;

  return {
    flowData,
    canUndo,
    canRedo,
    updateFlow,
    undo,
    redo,
    clearHistory
  };
};
