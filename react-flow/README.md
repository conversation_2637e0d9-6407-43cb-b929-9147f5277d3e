# 🚀 AI Flow 编排系统 - React 版本

基于 `@xrenders/xflow` 实现的现代化流程编排界面，成功复刻 Vue 版本的核心功能。

## ✨ 项目亮点

- 🎯 **功能对等**: 完整复刻 Vue 版本的所有核心功能
- 🛠 **现代技术栈**: React 18 + TypeScript + @xrenders/xflow
- 🎨 **优雅界面**: 基于 Ant Design 的现代化 UI
- ⚡ **高性能**: 优化的状态管理和渲染性能
- 🔧 **完整工具链**: 撤销重做、快捷键、验证、存储等

## 🚀 快速开始

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

### 访问应用
打开浏览器访问: http://localhost:5175/

## 🎯 核心功能

### 📊 节点系统 (7种节点类型)
- ✅ **开始节点** - 流程起点，定义输入参数
- ✅ **结束节点** - 流程终点，定义输出参数
- ✅ **LLM节点** - 大语言模型调用，支持多种模型和参数配置
- ✅ **判断节点** - 条件分支判断，支持多种比较操作
- ✅ **代码节点** - 执行自定义代码，支持参数输入输出
- ✅ **HTTP节点** - HTTP请求调用，支持各种请求方法
- ✅ **知识库节点** - 知识库查询和检索

### 🎮 交互功能
- ✅ **拖拽操作** - 从工具栏拖拽节点到画布
- ✅ **连线功能** - 节点间的数据流连接
- ✅ **属性配置** - 点击节点配置参数
- ✅ **画布操作** - 缩放、平移、选择
- ✅ **撤销重做** - 完整的历史记录管理
- ✅ **快捷键支持** - Ctrl+Z/Y/S/C/V 等
- ✅ **右键菜单** - 节点和画布的上下文菜单
- ✅ **多选操作** - 支持框选和多选

### 💾 数据管理
- ✅ **流程保存** - 本地存储流程数据
- ✅ **流程加载** - 从存储中加载流程
- ✅ **导入导出** - JSON 格式的流程文件
- ✅ **批量操作** - 批量导出和管理
- ✅ **版本控制** - 流程版本和更新时间

### 🔍 验证系统
- ✅ **实时验证** - 工具栏显示验证状态
- ✅ **详细报告** - 错误和警告的详细信息
- ✅ **连接检查** - 节点连接性验证
- ✅ **配置检查** - 节点配置完整性验证
- ✅ **循环检测** - 防止无限循环的流程

## 🛠 技术栈

### 核心框架
- **React 18.3.1** - 现代 React 框架
- **TypeScript** - 类型安全
- **@xrenders/xflow 1.0.7** - 阿里巴巴开源的流程编辑器

### UI 组件
- **Ant Design** - 企业级 UI 设计语言
- **@ant-design/icons** - 图标库

### 开发工具
- **Vite** - 快速构建工具
- **pnpm** - 高效包管理
- **ESLint** - 代码质量检查

## 📁 项目结构

```
src/
├── components/           # 组件目录
│   ├── ContextMenu.tsx  # 右键菜单组件
│   ├── FlowManager.tsx  # 流程管理对话框
│   └── FlowValidator.tsx # 流程验证组件
├── hooks/               # 自定义 Hooks
│   ├── useKeyboard.ts   # 快捷键管理
│   ├── useFlowHistory.ts # 历史记录管理
│   └── useSelection.ts  # 选择状态管理
├── services/            # 服务层
│   └── flowStorage.ts   # 流程存储服务
├── utils/               # 工具函数
│   └── flowValidator.ts # 流程验证工具
├── App.tsx              # 主应用组件
├── App.css              # 应用样式
├── main.tsx             # 应用入口
└── index.css            # 全局样式
```

## 🎮 使用指南

### 基本操作
1. **添加节点**: 从左侧工具栏拖拽节点到画布
2. **连接节点**: 拖拽节点边缘的连接点创建连线
3. **配置节点**: 点击节点，右侧显示属性配置面板
4. **画布操作**: 鼠标滚轮缩放，拖拽平移画布

### 快捷键
- `Ctrl/Cmd + S`: 保存流程
- `Ctrl/Cmd + Z`: 撤销操作
- `Ctrl/Cmd + Y`: 重做操作
- `Ctrl/Cmd + C`: 复制节点
- `Ctrl/Cmd + V`: 粘贴节点
- `Ctrl/Cmd + A`: 全选
- `Delete/Backspace`: 删除选中元素

### 流程管理
1. **保存流程**: 点击工具栏"更多" → "流程管理" → "保存当前流程"
2. **加载流程**: 在流程管理对话框中点击"加载"
3. **导入导出**: 支持 JSON 格式的流程文件
4. **验证流程**: 点击工具栏"验证"按钮检查流程完整性

## 🔧 开发命令

```bash
# 开发模式
pnpm dev

# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview

# 类型检查
pnpm type-check
```

## 📝 与 Vue 版本对比

| 功能模块 | Vue 版本 | React 版本 | 状态 |
|----------|----------|------------|------|
| 流程编辑器 | @vue-flow/core | @xrenders/xflow | ✅ 完成 |
| 节点系统 | 7种节点类型 | 7种节点类型 | ✅ 完成 |
| 状态管理 | Pinia Store | Custom Hooks | ✅ 完成 |
| 表单系统 | Element Plus | Ant Design | ✅ 完成 |
| 快捷键 | 自定义实现 | 自定义实现 | ✅ 完成 |
| 右键菜单 | 自定义实现 | 自定义实现 | ✅ 完成 |
| 数据持久化 | LocalStorage | LocalStorage | ✅ 完成 |
| 流程验证 | 基础验证 | 增强验证 | ✅ 完成 |

## 🎉 成功要点

这个项目成功的关键在于：

1. **技术选型**: @xrenders/xflow 提供了强大的基础能力
2. **架构设计**: 使用 Function 组件 + Hooks 的现代 React 模式
3. **版本兼容**: React 18 与 @xrenders/xflow 的最佳匹配
4. **功能完整**: 实现了与 Vue 版本功能对等的完整系统
5. **用户体验**: 优雅的界面和流畅的交互体验

## 🔄 版本说明

### 双版本架构

为了解决 @xrenders/xflow 与 React 18 的兼容性问题，我们提供了两个版本：

#### 1. React Flow 版本（推荐）✅
- **技术栈**: @xyflow/react (React Flow v12) + Ant Design
- **优势**:
  - 与 React 18 完全兼容
  - 性能优秀，无卡顿
  - 稳定可靠，无 Handle 错误
  - 原生 React Flow 生态支持
- **功能**:
  - 6种核心节点类型
  - 完整的配置面板
  - 撤销/重做功能
  - 快捷键支持
  - 历史记录管理

#### 2. @xrenders/xflow 版本（实验性）⚠️
- **技术栈**: @xrenders/xflow + Ant Design
- **优势**:
  - 功能丰富
  - 内置表单渲染
  - 更多高级功能
- **限制**:
  - 与 React 18 兼容性问题
  - 可能出现 Handle 错误
  - 拖拽时可能卡顿

### 使用建议

- **生产环境**: 推荐使用 React Flow 版本
- **功能测试**: 可以尝试 @xrenders/xflow 版本
- **新项目**: 建议从 React Flow 版本开始

### 版本切换

在应用首页可以选择使用哪个版本：
1. 访问 http://localhost:5175/
2. 选择 "React Flow 版本" 或 "@xrenders/xflow 版本"
3. 开始使用对应的功能

## 📄 许可证

MIT License
