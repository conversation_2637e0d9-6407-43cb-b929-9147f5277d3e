lockfileVersion: 5.4

specifiers:
  '@ant-design/icons': ^6.0.0
  '@codemirror/view': ^6.38.0
  '@eslint/js': ^9.31.0
  '@types/lodash': ^4.17.20
  '@types/lodash-es': ^4.17.12
  '@types/react': ^19.1.8
  '@types/react-dom': ^19.1.6
  '@types/react-router-dom': ^5.3.3
  '@vitejs/plugin-react': ^4.6.0
  '@xrenders/xflow': 1.0.8-beta.3
  '@xyflow/react': ^12.8.2
  antd: ^5.26.4
  dayjs: ^1.11.13
  elkjs: ^0.10.0
  eslint: ^9.31.0
  eslint-plugin-react-hooks: ^5.2.0
  eslint-plugin-react-refresh: ^0.4.20
  globals: ^16.3.0
  lodash: ^4.17.21
  lodash-es: ^4.17.21
  react: ^18.3.1
  react-dom: ^18.3.1
  react-router-dom: ^7.6.3
  scheduler: ^0.26.0
  typescript: ^5.8.3
  typescript-eslint: ^8.36.0
  vite: ^6.0.0

dependencies:
  '@ant-design/icons': 6.0.0_nnrd3gsncyragczmpvfhocinkq
  '@codemirror/view': 6.38.0
  '@types/lodash': 4.17.20
  '@types/lodash-es': 4.17.12
  '@xrenders/xflow': 1.0.8-beta.3_sa55drp25fzat7qvxti6eb7k6q
  '@xyflow/react': 12.8.2_plv2tpncornq3a46ehgw3fu5b4
  antd: 5.26.4_nnrd3gsncyragczmpvfhocinkq
  dayjs: 1.11.13
  elkjs: 0.10.0
  lodash: 4.17.21
  lodash-es: 4.17.21
  react: 18.3.1
  react-dom: 18.3.1_react@18.3.1
  react-router-dom: 7.6.3_nnrd3gsncyragczmpvfhocinkq
  scheduler: 0.26.0

devDependencies:
  '@eslint/js': 9.31.0
  '@types/react': 19.1.8
  '@types/react-dom': 19.1.6_@types+react@19.1.8
  '@types/react-router-dom': 5.3.3
  '@vitejs/plugin-react': 4.6.0_vite@6.3.5
  eslint: 9.31.0
  eslint-plugin-react-hooks: 5.2.0_eslint@9.31.0
  eslint-plugin-react-refresh: 0.4.20_eslint@9.31.0
  globals: 16.3.0
  typescript: 5.8.3
  typescript-eslint: 8.36.0_6xxlkyoqwoehf7ormvjqg4ujge
  vite: 6.3.5

packages:

  /@ampproject/remapping/2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29
    dev: true

  /@ant-design/colors/6.0.0:
    resolution: {integrity: sha512-qAZRvPzfdWHtfameEGP2Qvuf838NhergR35o+EuVyB5XvSA98xod5r4utvi4TJ3ywmevm290g9nsCG5MryrdWQ==}
    dependencies:
      '@ctrl/tinycolor': 3.6.1
    dev: false

  /@ant-design/colors/7.2.1:
    resolution: {integrity: sha512-lCHDcEzieu4GA3n8ELeZ5VQ8pKQAWcGGLRTQ50aQM2iqPpq2evTxER84jfdPvsPAtEcZ7m44NI45edFMo8oOYQ==}
    dependencies:
      '@ant-design/fast-color': 2.0.6
    dev: false

  /@ant-design/colors/8.0.0:
    resolution: {integrity: sha512-6YzkKCw30EI/E9kHOIXsQDHmMvTllT8STzjMb4K2qzit33RW2pqCJP0sk+hidBntXxE+Vz4n1+RvCTfBw6OErw==}
    dependencies:
      '@ant-design/fast-color': 3.0.0
    dev: false

  /@ant-design/cssinjs-utils/1.1.3_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-nOoQMLW1l+xR1Co8NFVYiP8pZp3VjIIzqV6D6ShYF2ljtdwWJn5WSsH+7kvCktXL/yhEtWURKOfH5Xz/gzlwsg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/cssinjs': 1.23.0_nnrd3gsncyragczmpvfhocinkq
      '@babel/runtime': 7.27.6
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@ant-design/cssinjs/1.23.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-7GAg9bD/iC9ikWatU9ym+P9ugJhi/WbsTWzcKN6T4gU0aehsprtke1UAaaSxxkjjmkJb3llet/rbUSLPgwlY4w==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/hash': 0.8.0
      '@emotion/unitless': 0.7.5
      classnames: 2.5.1
      csstype: 3.1.3
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      stylis: 4.3.6
    dev: false

  /@ant-design/fast-color/2.0.6:
    resolution: {integrity: sha512-y2217gk4NqL35giHl72o6Zzqji9O7vHh9YmhUVkPtAOpoTCH4uWxo/pr4VE8t0+ChEPs0qo4eJRC5Q1eXWo3vA==}
    engines: {node: '>=8.x'}
    dependencies:
      '@babel/runtime': 7.27.6
    dev: false

  /@ant-design/fast-color/3.0.0:
    resolution: {integrity: sha512-eqvpP7xEDm2S7dUzl5srEQCBTXZMmY3ekf97zI+M2DHOYyKdJGH0qua0JACHTqbkRnD/KHFQP9J1uMJ/XWVzzA==}
    engines: {node: '>=8.x'}
    dev: false

  /@ant-design/icons-svg/4.4.2:
    resolution: {integrity: sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA==}
    dev: false

  /@ant-design/icons/4.8.3_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-HGlIQZzrEbAhpJR6+IGdzfbPym94Owr6JZkJ2QCCnOkPVIWMO2xgIVcOKnl8YcpijIo39V7l2qQL5fmtw56cMw==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@ant-design/colors': 6.0.0
      '@ant-design/icons-svg': 4.4.2
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      lodash: 4.17.21
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@ant-design/icons/5.6.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-0/xS39c91WjPAZOWsvi1//zjx6kAp4kxWwctR6kuU6p133w8RU0D2dSCvZC19uQyharg/sAvYxGYWl01BbZZfg==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@ant-design/colors': 7.2.1
      '@ant-design/icons-svg': 4.4.2
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@ant-design/icons/6.0.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-o0aCCAlHc1o4CQcapAwWzHeaW2x9F49g7P3IDtvtNXgHowtRWYb7kiubt8sQPFvfVIVU/jLw2hzeSlNt0FU+Uw==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@ant-design/colors': 8.0.0
      '@ant-design/icons-svg': 4.4.2
      '@rc-component/util': 1.2.1_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@ant-design/react-slick/1.1.2_react@18.3.1:
    resolution: {integrity: sha512-EzlvzE6xQUBrZuuhSAFTdsr4P2bBBHGZwKFemEfq8gIGyIQCxalYfZW/T2ORbtQx5rU69o+WycP3exY/7T1hGA==}
    peerDependencies:
      react: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      json2mq: 0.2.0
      react: 18.3.1
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 5.0.2
    dev: false

  /@babel/code-frame/7.27.1:
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1
    dev: true

  /@babel/compat-data/7.28.0:
    resolution: {integrity: sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/core/7.28.0:
    resolution: {integrity: sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3_@babel+core@7.28.0
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.1
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/generator/7.28.0:
    resolution: {integrity: sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.1
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29
      jsesc: 3.1.0
    dev: true

  /@babel/helper-compilation-targets/7.27.2:
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.1
      lru-cache: 5.1.1
      semver: 6.3.1
    dev: true

  /@babel/helper-globals/7.28.0:
    resolution: {integrity: sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-module-imports/7.27.1:
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-module-transforms/7.27.3_@babel+core@7.28.0:
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-plugin-utils/7.27.1:
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-string-parser/7.27.1:
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-identifier/7.27.1:
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-option/7.27.1:
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helpers/7.27.6:
    resolution: {integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.28.1
    dev: true

  /@babel/parser/7.28.0:
    resolution: {integrity: sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.28.1
    dev: true

  /@babel/plugin-transform-react-jsx-self/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-react-jsx-source/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/runtime/7.27.6:
    resolution: {integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/template/7.27.2:
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.1
    dev: true

  /@babel/traverse/7.28.0:
    resolution: {integrity: sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-globals': 7.28.0
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/types': 7.28.1
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/types/7.28.1:
    resolution: {integrity: sha512-x0LvFTekgSX+83TI28Y9wYPUfzrnl2aT5+5QLnO6v7mSJYtEEevuDRN0F0uSHRk1G1IWZC43o00Y0xDDrpBGPQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
    dev: true

  /@codemirror/autocomplete/6.18.6:
    resolution: {integrity: sha512-PHHBXFomUs5DF+9tCOM/UoW6XQ4R44lLNNhRaW9PKPTU0D7lIjRg3ElxaJnTwsl/oHiR93WSXDBrekhoUGCPtg==}
    dependencies:
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.0
      '@lezer/common': 1.2.3
    dev: false

  /@codemirror/commands/6.8.1:
    resolution: {integrity: sha512-KlGVYufHMQzxbdQONiLyGQDUW0itrLZwq3CcY7xpv9ZLRHqzkBSoteocBHtMCoY7/Ci4xhzSrToIeLg7FxHuaw==}
    dependencies:
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.0
      '@lezer/common': 1.2.3
    dev: false

  /@codemirror/lang-json/6.0.2:
    resolution: {integrity: sha512-x2OtO+AvwEHrEwR0FyyPtfDUiloG3rnVTSZV1W8UteaLL8/MajQd8DpvUb2YVzC+/T18aSDv0H9mu+xw0EStoQ==}
    dependencies:
      '@codemirror/language': 6.11.2
      '@lezer/json': 1.0.3
    dev: false

  /@codemirror/language/6.11.2:
    resolution: {integrity: sha512-p44TsNArL4IVXDTbapUmEkAlvWs2CFQbcfc0ymDsis1kH2wh0gcY96AS29c/vp2d0y2Tquk1EDSaawpzilUiAw==}
    dependencies:
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.0
      '@lezer/common': 1.2.3
      '@lezer/highlight': 1.2.1
      '@lezer/lr': 1.4.2
      style-mod: 4.1.2
    dev: false

  /@codemirror/lint/6.8.5:
    resolution: {integrity: sha512-s3n3KisH7dx3vsoeGMxsbRAgKe4O1vbrnKBClm99PU0fWxmxsx5rR2PfqQgIt+2MMJBHbiJ5rfIdLYfB9NNvsA==}
    dependencies:
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.0
      crelt: 1.0.6
    dev: false

  /@codemirror/search/6.5.11:
    resolution: {integrity: sha512-KmWepDE6jUdL6n8cAAqIpRmLPBZ5ZKnicE8oGU/s3QrAVID+0VhLFrzUucVKHG5035/BSykhExDL/Xm7dHthiA==}
    dependencies:
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.0
      crelt: 1.0.6
    dev: false

  /@codemirror/state/6.5.2:
    resolution: {integrity: sha512-FVqsPqtPWKVVL3dPSxy8wEF/ymIEuVzF1PK3VbUgrxXpJUSHQWWZz4JMToquRxnkw+36LTamCZG2iua2Ptq0fA==}
    dependencies:
      '@marijn/find-cluster-break': 1.0.2
    dev: false

  /@codemirror/theme-one-dark/6.1.3:
    resolution: {integrity: sha512-NzBdIvEJmx6fjeremiGp3t/okrLPYT0d9orIc7AFun8oZcRk58aejkqhv6spnz4MLAevrKNPMQYXEWMg4s+sKA==}
    dependencies:
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.0
      '@lezer/highlight': 1.2.1
    dev: false

  /@codemirror/view/6.38.0:
    resolution: {integrity: sha512-yvSchUwHOdupXkd7xJ0ob36jdsSR/I+/C+VbY0ffBiL5NiSTEBDfB1ZGWbbIlDd5xgdUkody+lukAdOxYrOBeg==}
    dependencies:
      '@codemirror/state': 6.5.2
      crelt: 1.0.6
      style-mod: 4.1.2
      w3c-keyname: 2.2.8
    dev: false

  /@ctrl/tinycolor/3.6.1:
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}
    dev: false

  /@dagrejs/dagre/1.1.5:
    resolution: {integrity: sha512-Ghgrh08s12DCL5SeiR6AoyE80mQELTWhJBRmXfFoqDiFkR458vPEdgTbbjA0T+9ETNxUblnD0QW55tfdvi5pjQ==}
    dependencies:
      '@dagrejs/graphlib': 2.2.4
    dev: false

  /@dagrejs/graphlib/2.2.4:
    resolution: {integrity: sha512-mepCf/e9+SKYy1d02/UkvSy6+6MoyXhVxP8lLDfA7BPE1X1d4dR0sZznmbM8/XVJ1GPM+Svnx7Xj6ZweByWUkw==}
    engines: {node: '>17.0.0'}
    dev: false

  /@emotion/hash/0.8.0:
    resolution: {integrity: sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow==}
    dev: false

  /@emotion/unitless/0.7.5:
    resolution: {integrity: sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg==}
    dev: false

  /@esbuild/aix-ppc64/0.25.6:
    resolution: {integrity: sha512-ShbM/3XxwuxjFiuVBHA+d3j5dyac0aEVVq1oluIDf71hUw0aRF59dV/efUsIwFnR6m8JNM2FjZOzmaZ8yG61kw==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm/0.25.6:
    resolution: {integrity: sha512-S8ToEOVfg++AU/bHwdksHNnyLyVM+eMVAOf6yRKFitnwnbwwPNqKr3srzFRe7nzV69RQKb5DgchIX5pt3L53xg==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm64/0.25.6:
    resolution: {integrity: sha512-hd5zdUarsK6strW+3Wxi5qWws+rJhCCbMiC9QZyzoxfk5uHRIE8T287giQxzVpEvCwuJ9Qjg6bEjcRJcgfLqoA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-x64/0.25.6:
    resolution: {integrity: sha512-0Z7KpHSr3VBIO9A/1wcT3NTy7EB4oNC4upJ5ye3R7taCc2GUdeynSLArnon5G8scPwaU866d3H4BCrE5xLW25A==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-arm64/0.25.6:
    resolution: {integrity: sha512-FFCssz3XBavjxcFxKsGy2DYK5VSvJqa6y5HXljKzhRZ87LvEi13brPrf/wdyl/BbpbMKJNOr1Sd0jtW4Ge1pAA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-x64/0.25.6:
    resolution: {integrity: sha512-GfXs5kry/TkGM2vKqK2oyiLFygJRqKVhawu3+DOCk7OxLy/6jYkWXhlHwOoTb0WqGnWGAS7sooxbZowy+pK9Yg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-arm64/0.25.6:
    resolution: {integrity: sha512-aoLF2c3OvDn2XDTRvn8hN6DRzVVpDlj2B/F66clWd/FHLiHaG3aVZjxQX2DYphA5y/evbdGvC6Us13tvyt4pWg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-x64/0.25.6:
    resolution: {integrity: sha512-2SkqTjTSo2dYi/jzFbU9Plt1vk0+nNg8YC8rOXXea+iA3hfNJWebKYPs3xnOUf9+ZWhKAaxnQNUf2X9LOpeiMQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm/0.25.6:
    resolution: {integrity: sha512-SZHQlzvqv4Du5PrKE2faN0qlbsaW/3QQfUUc6yO2EjFcA83xnwm91UbEEVx4ApZ9Z5oG8Bxz4qPE+HFwtVcfyw==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm64/0.25.6:
    resolution: {integrity: sha512-b967hU0gqKd9Drsh/UuAm21Khpoh6mPBSgz8mKRq4P5mVK8bpA+hQzmm/ZwGVULSNBzKdZPQBRT3+WuVavcWsQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ia32/0.25.6:
    resolution: {integrity: sha512-aHWdQ2AAltRkLPOsKdi3xv0mZ8fUGPdlKEjIEhxCPm5yKEThcUjHpWB1idN74lfXGnZ5SULQSgtr5Qos5B0bPw==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64/0.25.6:
    resolution: {integrity: sha512-VgKCsHdXRSQ7E1+QXGdRPlQ/e08bN6WMQb27/TMfV+vPjjTImuT9PmLXupRlC90S1JeNNW5lzkAEO/McKeJ2yg==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-mips64el/0.25.6:
    resolution: {integrity: sha512-WViNlpivRKT9/py3kCmkHnn44GkGXVdXfdc4drNmRl15zVQ2+D2uFwdlGh6IuK5AAnGTo2qPB1Djppj+t78rzw==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ppc64/0.25.6:
    resolution: {integrity: sha512-wyYKZ9NTdmAMb5730I38lBqVu6cKl4ZfYXIs31Baf8aoOtB4xSGi3THmDYt4BTFHk7/EcVixkOV2uZfwU3Q2Jw==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-riscv64/0.25.6:
    resolution: {integrity: sha512-KZh7bAGGcrinEj4qzilJ4hqTY3Dg2U82c8bv+e1xqNqZCrCyc+TL9AUEn5WGKDzm3CfC5RODE/qc96OcbIe33w==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-s390x/0.25.6:
    resolution: {integrity: sha512-9N1LsTwAuE9oj6lHMyyAM+ucxGiVnEqUdp4v7IaMmrwb06ZTEVCIs3oPPplVsnjPfyjmxwHxHMF8b6vzUVAUGw==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-x64/0.25.6:
    resolution: {integrity: sha512-A6bJB41b4lKFWRKNrWoP2LHsjVzNiaurf7wyj/XtFNTsnPuxwEBWHLty+ZE0dWBKuSK1fvKgrKaNjBS7qbFKig==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-arm64/0.25.6:
    resolution: {integrity: sha512-IjA+DcwoVpjEvyxZddDqBY+uJ2Snc6duLpjmkXm/v4xuS3H+3FkLZlDm9ZsAbF9rsfP3zeA0/ArNDORZgrxR/Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-x64/0.25.6:
    resolution: {integrity: sha512-dUXuZr5WenIDlMHdMkvDc1FAu4xdWixTCRgP7RQLBOkkGgwuuzaGSYcOpW4jFxzpzL1ejb8yF620UxAqnBrR9g==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-arm64/0.25.6:
    resolution: {integrity: sha512-l8ZCvXP0tbTJ3iaqdNf3pjaOSd5ex/e6/omLIQCVBLmHTlfXW3zAxQ4fnDmPLOB1x9xrcSi/xtCWFwCZRIaEwg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-x64/0.25.6:
    resolution: {integrity: sha512-hKrmDa0aOFOr71KQ/19JC7az1P0GWtCN1t2ahYAf4O007DHZt/dW8ym5+CUdJhQ/qkZmI1HAF8KkJbEFtCL7gw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openharmony-arm64/0.25.6:
    resolution: {integrity: sha512-+SqBcAWoB1fYKmpWoQP4pGtx+pUUC//RNYhFdbcSA16617cchuryuhOCRpPsjCblKukAckWsV+aQ3UKT/RMPcA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openharmony]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/sunos-x64/0.25.6:
    resolution: {integrity: sha512-dyCGxv1/Br7MiSC42qinGL8KkG4kX0pEsdb0+TKhmJZgCUDBGmyo1/ArCjNGiOLiIAgdbWgmWgib4HoCi5t7kA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-arm64/0.25.6:
    resolution: {integrity: sha512-42QOgcZeZOvXfsCBJF5Afw73t4veOId//XD3i+/9gSkhSV6Gk3VPlWncctI+JcOyERv85FUo7RxuxGy+z8A43Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-ia32/0.25.6:
    resolution: {integrity: sha512-4AWhgXmDuYN7rJI6ORB+uU9DHLq/erBbuMoAuB4VWJTu5KtCgcKYPynF0YI1VkBNuEfjNlLrFr9KZPJzrtLkrQ==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-x64/0.25.6:
    resolution: {integrity: sha512-NgJPHHbEpLQgDH2MjQu90pzW/5vvXIZ7KOnPyNBm92A6WgZ/7b6fJyUBjoumLqeOQQGqY2QjQxRo97ah4Sj0cA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@eslint-community/eslint-utils/4.7.0_eslint@9.31.0:
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: 9.31.0
      eslint-visitor-keys: 3.4.3
    dev: true

  /@eslint-community/regexpp/4.12.1:
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  /@eslint/config-array/0.21.0:
    resolution: {integrity: sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@eslint/config-helpers/0.3.0:
    resolution: {integrity: sha512-ViuymvFmcJi04qdZeDc2whTHryouGcDlaxPqarTD0ZE10ISpxGUVZGZDx4w01upyIynL3iu6IXH2bS1NhclQMw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  /@eslint/core/0.15.1:
    resolution: {integrity: sha512-bkOp+iumZCCbt1K1CmWf0R9pM5yKpDv+ZXtvSyQpudrI9kuFLp+bM2WOPXImuD/ceQuaa8f5pj93Y7zyECIGNA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@types/json-schema': 7.0.15
    dev: true

  /@eslint/eslintrc/3.3.1:
    resolution: {integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 10.4.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@eslint/js/9.31.0:
    resolution: {integrity: sha512-LOm5OVt7D4qiKCqoiPbA7LWmI+tbw1VbTUowBcUMgQSuM6poJufkFkYDcQpo5KfgD39TnNySV26QjOh7VFpSyw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  /@eslint/object-schema/2.1.6:
    resolution: {integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  /@eslint/plugin-kit/0.3.3:
    resolution: {integrity: sha512-1+WqvgNMhmlAambTvT3KPtCl/Ibr68VldY2XY40SL1CE0ZXiakFR/cbTspaF5HsnpDMvcYYoJHfl4980NBjGag==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@eslint/core': 0.15.1
      levn: 0.4.1
    dev: true

  /@humanfs/core/0.19.1:
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==}
    engines: {node: '>=18.18.0'}
    dev: true

  /@humanfs/node/0.16.6:
    resolution: {integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==}
    engines: {node: '>=18.18.0'}
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1
    dev: true

  /@humanwhocodes/module-importer/1.0.1:
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}
    dev: true

  /@humanwhocodes/retry/0.3.1:
    resolution: {integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==}
    engines: {node: '>=18.18'}
    dev: true

  /@humanwhocodes/retry/0.4.3:
    resolution: {integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==}
    engines: {node: '>=18.18'}
    dev: true

  /@jridgewell/gen-mapping/0.3.12:
    resolution: {integrity: sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==}
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4
      '@jridgewell/trace-mapping': 0.3.29
    dev: true

  /@jridgewell/resolve-uri/3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/sourcemap-codec/1.5.4:
    resolution: {integrity: sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==}
    dev: true

  /@jridgewell/trace-mapping/0.3.29:
    resolution: {integrity: sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.4
    dev: true

  /@lezer/common/1.2.3:
    resolution: {integrity: sha512-w7ojc8ejBqr2REPsWxJjrMFsA/ysDCFICn8zEOR9mrqzOu2amhITYuLD8ag6XZf0CFXDrhKqw7+tW8cX66NaDA==}
    dev: false

  /@lezer/highlight/1.2.1:
    resolution: {integrity: sha512-Z5duk4RN/3zuVO7Jq0pGLJ3qynpxUVsh7IbUbGj88+uV2ApSAn6kWg2au3iJb+0Zi7kKtqffIESgNcRXWZWmSA==}
    dependencies:
      '@lezer/common': 1.2.3
    dev: false

  /@lezer/json/1.0.3:
    resolution: {integrity: sha512-BP9KzdF9Y35PDpv04r0VeSTKDeox5vVr3efE7eBbx3r4s3oNLfunchejZhjArmeieBH+nVOpgIiBJpEAv8ilqQ==}
    dependencies:
      '@lezer/common': 1.2.3
      '@lezer/highlight': 1.2.1
      '@lezer/lr': 1.4.2
    dev: false

  /@lezer/lr/1.4.2:
    resolution: {integrity: sha512-pu0K1jCIdnQ12aWNaAVU5bzi7Bd1w54J3ECgANPmYLtQKP0HBj2cE/5coBD66MT10xbtIuUr7tg0Shbsvk0mDA==}
    dependencies:
      '@lezer/common': 1.2.3
    dev: false

  /@marijn/find-cluster-break/1.0.2:
    resolution: {integrity: sha512-l0h88YhZFyKdXIFNfSWpyjStDjGHwZ/U7iobcK1cQQD8sejsONdQtTVU+1wVN1PBw40PiiHB1vA5S7VTfQiP9g==}
    dev: false

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1
    dev: true

  /@rc-component/async-validator/5.0.4:
    resolution: {integrity: sha512-qgGdcVIF604M9EqjNF0hbUTz42bz/RDtxWdWuU5EQe3hi7M8ob54B6B35rOsvX5eSvIHIzT9iH1R3n+hk3CGfg==}
    engines: {node: '>=14.x'}
    dependencies:
      '@babel/runtime': 7.27.6
    dev: false

  /@rc-component/color-picker/2.0.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-WcZYwAThV/b2GISQ8F+7650r5ZZJ043E57aVBFkQ+kSY4C6wdofXgB0hBx+GPGpIU0Z81eETNoDUJMr7oy/P8Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/fast-color': 2.0.6
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@rc-component/context/1.4.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-kFcNxg9oLRMoL3qki0OMxK+7g5mypjgaaJp/pkOis/6rVxma9nJBF/8kCIuTYHUQNr0ii7MxqE33wirPZLJQ2w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@rc-component/mini-decimal/1.1.0:
    resolution: {integrity: sha512-jS4E7T9Li2GuYwI6PyiVXmxTiM6b07rlD9Ge8uGZSCz3WlzcG5ZK7g5bbuKNeZ9pgUuPK/5guV781ujdVpm4HQ==}
    engines: {node: '>=8.x'}
    dependencies:
      '@babel/runtime': 7.27.6
    dev: false

  /@rc-component/mutate-observer/1.1.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-QjrOsDXQusNwGZPf4/qRQasg7UFEj06XiCJ8iuiq/Io7CrHrgVi6Uuetw60WAMG1799v+aM8kyc+1L/GBbHSlw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@rc-component/portal/1.1.2_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@rc-component/qrcode/1.0.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-L+rZ4HXP2sJ1gHMGHjsg9jlYBX/SLN2D6OxP9Zn3qgtpMWtO2vUfxVFwiogHpAIqs54FnALxraUy/BCO1yRIgg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@rc-component/tour/1.15.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-Tr2t7J1DKZUpfJuDZWHxyxWpfmj8EZrqSgyMZ+BCdvKZ6r1UDsfU46M/iWAAFBy961Ssfom2kv5f3UcjIL2CmQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/portal': 1.1.2_nnrd3gsncyragczmpvfhocinkq
      '@rc-component/trigger': 2.2.7_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@rc-component/trigger/2.2.7_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-Qggj4Z0AA2i5dJhzlfFSmg1Qrziu8dsdHOihROL5Kl18seO2Eh/ZaTYt2c8a/CyGaTChnFry7BEYew1+/fhSbA==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/portal': 1.1.2_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-resize-observer: 1.4.3_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@rc-component/util/1.2.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-AUVu6jO+lWjQnUOOECwu8iR0EdElQgWW5NBv5vP/Uf9dWbAX3udhMutRlkVXjuac2E40ghkFy+ve00mc/3Fymg==}
    peerDependencies:
      react: '>=18.0.0'
      react-dom: '>=18.0.0'
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      react-is: 18.3.1
    dev: false

  /@rolldown/pluginutils/1.0.0-beta.19:
    resolution: {integrity: sha512-3FL3mnMbPu0muGOCaKAhhFEYmqv9eTfPSJRJmANrCwtgK8VuxpsZDGK+m0LYAGoyO8+0j5uRe4PeyPDK1yA/hA==}
    dev: true

  /@rollup/rollup-android-arm-eabi/4.45.0:
    resolution: {integrity: sha512-2o/FgACbji4tW1dzXOqAV15Eu7DdgbKsF2QKcxfG4xbh5iwU7yr5RRP5/U+0asQliSYv5M4o7BevlGIoSL0LXg==}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-android-arm64/4.45.0:
    resolution: {integrity: sha512-PSZ0SvMOjEAxwZeTx32eI/j5xSYtDCRxGu5k9zvzoY77xUNssZM+WV6HYBLROpY5CkXsbQjvz40fBb7WPwDqtQ==}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-arm64/4.45.0:
    resolution: {integrity: sha512-BA4yPIPssPB2aRAWzmqzQ3y2/KotkLyZukVB7j3psK/U3nVJdceo6qr9pLM2xN6iRP/wKfxEbOb1yrlZH6sYZg==}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-x64/4.45.0:
    resolution: {integrity: sha512-Pr2o0lvTwsiG4HCr43Zy9xXrHspyMvsvEw4FwKYqhli4FuLE5FjcZzuQ4cfPe0iUFCvSQG6lACI0xj74FDZKRA==}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-freebsd-arm64/4.45.0:
    resolution: {integrity: sha512-lYE8LkE5h4a/+6VnnLiL14zWMPnx6wNbDG23GcYFpRW1V9hYWHAw9lBZ6ZUIrOaoK7NliF1sdwYGiVmziUF4vA==}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-freebsd-x64/4.45.0:
    resolution: {integrity: sha512-PVQWZK9sbzpvqC9Q0GlehNNSVHR+4m7+wET+7FgSnKG3ci5nAMgGmr9mGBXzAuE5SvguCKJ6mHL6vq1JaJ/gvw==}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm-gnueabihf/4.45.0:
    resolution: {integrity: sha512-hLrmRl53prCcD+YXTfNvXd776HTxNh8wPAMllusQ+amcQmtgo3V5i/nkhPN6FakW+QVLoUUr2AsbtIRPFU3xIA==}
    cpu: [arm]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm-musleabihf/4.45.0:
    resolution: {integrity: sha512-XBKGSYcrkdiRRjl+8XvrUR3AosXU0NvF7VuqMsm7s5nRy+nt58ZMB19Jdp1RdqewLcaYnpk8zeVs/4MlLZEJxw==}
    cpu: [arm]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-gnu/4.45.0:
    resolution: {integrity: sha512-fRvZZPUiBz7NztBE/2QnCS5AtqLVhXmUOPj9IHlfGEXkapgImf4W9+FSkL8cWqoAjozyUzqFmSc4zh2ooaeF6g==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-musl/4.45.0:
    resolution: {integrity: sha512-Btv2WRZOcUGi8XU80XwIvzTg4U6+l6D0V6sZTrZx214nrwxw5nAi8hysaXj/mctyClWgesyuxbeLylCBNauimg==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-loongarch64-gnu/4.45.0:
    resolution: {integrity: sha512-Li0emNnwtUZdLwHjQPBxn4VWztcrw/h7mgLyHiEI5Z0MhpeFGlzaiBHpSNVOMB/xucjXTTcO+dhv469Djr16KA==}
    cpu: [loong64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-powerpc64le-gnu/4.45.0:
    resolution: {integrity: sha512-sB8+pfkYx2kvpDCfd63d5ScYT0Fz1LO6jIb2zLZvmK9ob2D8DeVqrmBDE0iDK8KlBVmsTNzrjr3G1xV4eUZhSw==}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-riscv64-gnu/4.45.0:
    resolution: {integrity: sha512-5GQ6PFhh7E6jQm70p1aW05G2cap5zMOvO0se5JMecHeAdj5ZhWEHbJ4hiKpfi1nnnEdTauDXxPgXae/mqjow9w==}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-riscv64-musl/4.45.0:
    resolution: {integrity: sha512-N/euLsBd1rekWcuduakTo/dJw6U6sBP3eUq+RXM9RNfPuWTvG2w/WObDkIvJ2KChy6oxZmOSC08Ak2OJA0UiAA==}
    cpu: [riscv64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-s390x-gnu/4.45.0:
    resolution: {integrity: sha512-2l9sA7d7QdikL0xQwNMO3xURBUNEWyHVHfAsHsUdq+E/pgLTUcCE+gih5PCdmyHmfTDeXUWVhqL0WZzg0nua3g==}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-gnu/4.45.0:
    resolution: {integrity: sha512-XZdD3fEEQcwG2KrJDdEQu7NrHonPxxaV0/w2HpvINBdcqebz1aL+0vM2WFJq4DeiAVT6F5SUQas65HY5JDqoPw==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-musl/4.45.0:
    resolution: {integrity: sha512-7ayfgvtmmWgKWBkCGg5+xTQ0r5V1owVm67zTrsEY1008L5ro7mCyGYORomARt/OquB9KY7LpxVBZes+oSniAAQ==}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-arm64-msvc/4.45.0:
    resolution: {integrity: sha512-B+IJgcBnE2bm93jEW5kHisqvPITs4ddLOROAcOc/diBgrEiQJJ6Qcjby75rFSmH5eMGrqJryUgJDhrfj942apQ==}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-ia32-msvc/4.45.0:
    resolution: {integrity: sha512-+CXwwG66g0/FpWOnP/v1HnrGVSOygK/osUbu3wPRy8ECXjoYKjRAyfxYpDQOfghC5qPJYLPH0oN4MCOjwgdMug==}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-x64-msvc/4.45.0:
    resolution: {integrity: sha512-SRf1cytG7wqcHVLrBc9VtPK4pU5wxiB/lNIkNmW2ApKXIg+RpqwHfsaEK+e7eH4A1BpI6BX/aBWXxZCIrJg3uA==}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@types/babel__core/7.20.5:
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==}
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.1
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7
    dev: true

  /@types/babel__generator/7.27.0:
    resolution: {integrity: sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==}
    dependencies:
      '@babel/types': 7.28.1
    dev: true

  /@types/babel__template/7.4.4:
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==}
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.1
    dev: true

  /@types/babel__traverse/7.20.7:
    resolution: {integrity: sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==}
    dependencies:
      '@babel/types': 7.28.1
    dev: true

  /@types/d3-color/3.1.3:
    resolution: {integrity: sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==}
    dev: false

  /@types/d3-drag/3.0.7:
    resolution: {integrity: sha512-HE3jVKlzU9AaMazNufooRJ5ZpWmLIoc90A37WU2JMmeq28w1FQqCZswHZ3xR+SuxYftzHq6WU6KJHvqxKzTxxQ==}
    dependencies:
      '@types/d3-selection': 3.0.11
    dev: false

  /@types/d3-interpolate/3.0.4:
    resolution: {integrity: sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==}
    dependencies:
      '@types/d3-color': 3.1.3
    dev: false

  /@types/d3-selection/3.0.11:
    resolution: {integrity: sha512-bhAXu23DJWsrI45xafYpkQ4NtcKMwWnAC/vKrd2l+nxMFuvOT3XMYTIj2opv8vq8AO5Yh7Qac/nSeP/3zjTK0w==}
    dev: false

  /@types/d3-transition/3.0.9:
    resolution: {integrity: sha512-uZS5shfxzO3rGlu0cC3bjmMFKsXv+SmZZcgp0KD22ts4uGXp5EVYGzu/0YdwZeKmddhcAccYtREJKkPfXkZuCg==}
    dependencies:
      '@types/d3-selection': 3.0.11
    dev: false

  /@types/d3-zoom/3.0.8:
    resolution: {integrity: sha512-iqMC4/YlFCSlO8+2Ii1GGGliCAY4XdeG748w5vQUbevlbDu0zSjH/+jojorQVBK/se0j6DUFNPBGSqD3YWYnDw==}
    dependencies:
      '@types/d3-interpolate': 3.0.4
      '@types/d3-selection': 3.0.11
    dev: false

  /@types/estree/1.0.8:
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}
    dev: true

  /@types/history/4.7.11:
    resolution: {integrity: sha512-qjDJRrmvBMiTx+jyLxvLfJU7UznFuokDv4f3WRuriHKERccVpFU+8XMQUAbDzoiJCsmexxRExQeMwwCdamSKDA==}
    dev: true

  /@types/json-schema/7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}
    dev: true

  /@types/lodash-es/4.17.12:
    resolution: {integrity: sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==}
    dependencies:
      '@types/lodash': 4.17.20
    dev: false

  /@types/lodash/4.17.20:
    resolution: {integrity: sha512-H3MHACvFUEiujabxhaI/ImO6gUrd8oOurg7LQtS7mbwIXA/cUqWrvBsaeJ23aZEPk1TAYkurjfMbSELfoCXlGA==}
    dev: false

  /@types/react-dom/19.1.6_@types+react@19.1.8:
    resolution: {integrity: sha512-4hOiT/dwO8Ko0gV1m/TJZYk3y0KBnY9vzDh7W+DH17b2HFSOGgdj33dhihPeuy3l0q23+4e+hoXHV6hCC4dCXw==}
    peerDependencies:
      '@types/react': ^19.0.0
    dependencies:
      '@types/react': 19.1.8
    dev: true

  /@types/react-router-dom/5.3.3:
    resolution: {integrity: sha512-kpqnYK4wcdm5UaWI3fLcELopqLrHgLqNsdpHauzlQktfkHL3npOSwtj1Uz9oKBAzs7lFtVkV8j83voAz2D8fhw==}
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 19.1.8
      '@types/react-router': 5.1.20
    dev: true

  /@types/react-router/5.1.20:
    resolution: {integrity: sha512-jGjmu/ZqS7FjSH6owMcD5qpq19+1RS9DeVRqfl1FeBMxTDQAGwlMWOcs52NDoXaNKyG3d1cYQFMs9rCrb88o9Q==}
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 19.1.8
    dev: true

  /@types/react/19.1.8:
    resolution: {integrity: sha512-AwAfQ2Wa5bCx9WP8nZL2uMZWod7J7/JSplxbTmBQ5ms6QpqNYm672H0Vu9ZVKVngQ+ii4R/byguVEUZQyeg44g==}
    dependencies:
      csstype: 3.1.3

  /@typescript-eslint/eslint-plugin/8.36.0_jll55oybnhbxgaj5dunmerd2cm:
    resolution: {integrity: sha512-lZNihHUVB6ZZiPBNgOQGSxUASI7UJWhT8nHyUGCnaQ28XFCw98IfrMCG3rUl1uwUWoAvodJQby2KTs79UTcrAg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.36.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.36.0_6xxlkyoqwoehf7ormvjqg4ujge
      '@typescript-eslint/scope-manager': 8.36.0
      '@typescript-eslint/type-utils': 8.36.0_6xxlkyoqwoehf7ormvjqg4ujge
      '@typescript-eslint/utils': 8.36.0_6xxlkyoqwoehf7ormvjqg4ujge
      '@typescript-eslint/visitor-keys': 8.36.0
      eslint: 9.31.0
      graphemer: 1.4.0
      ignore: 7.0.5
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0_typescript@5.8.3
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/parser/8.36.0_6xxlkyoqwoehf7ormvjqg4ujge:
    resolution: {integrity: sha512-FuYgkHwZLuPbZjQHzJXrtXreJdFMKl16BFYyRrLxDhWr6Qr7Kbcu2s1Yhu8tsiMXw1S0W1pjfFfYEt+R604s+Q==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@typescript-eslint/scope-manager': 8.36.0
      '@typescript-eslint/types': 8.36.0
      '@typescript-eslint/typescript-estree': 8.36.0_typescript@5.8.3
      '@typescript-eslint/visitor-keys': 8.36.0
      debug: 4.4.1
      eslint: 9.31.0
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/project-service/8.36.0_typescript@5.8.3:
    resolution: {integrity: sha512-JAhQFIABkWccQYeLMrHadu/fhpzmSQ1F1KXkpzqiVxA/iYI6UnRt2trqXHt1sYEcw1mxLnB9rKMsOxXPxowN/g==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@typescript-eslint/tsconfig-utils': 8.36.0_typescript@5.8.3
      '@typescript-eslint/types': 8.36.0
      debug: 4.4.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/scope-manager/8.36.0:
    resolution: {integrity: sha512-wCnapIKnDkN62fYtTGv2+RY8FlnBYA3tNm0fm91kc2BjPhV2vIjwwozJ7LToaLAyb1ca8BxrS7vT+Pvvf7RvqA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@typescript-eslint/types': 8.36.0
      '@typescript-eslint/visitor-keys': 8.36.0
    dev: true

  /@typescript-eslint/tsconfig-utils/8.36.0_typescript@5.8.3:
    resolution: {integrity: sha512-Nhh3TIEgN18mNbdXpd5Q8mSCBnrZQeY9V7Ca3dqYvNDStNIGRmJA6dmrIPMJ0kow3C7gcQbpsG2rPzy1Ks/AnA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      typescript: 5.8.3
    dev: true

  /@typescript-eslint/type-utils/8.36.0_6xxlkyoqwoehf7ormvjqg4ujge:
    resolution: {integrity: sha512-5aaGYG8cVDd6cxfk/ynpYzxBRZJk7w/ymto6uiyUFtdCozQIsQWh7M28/6r57Fwkbweng8qAzoMCPwSJfWlmsg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@typescript-eslint/typescript-estree': 8.36.0_typescript@5.8.3
      '@typescript-eslint/utils': 8.36.0_6xxlkyoqwoehf7ormvjqg4ujge
      debug: 4.4.1
      eslint: 9.31.0
      ts-api-utils: 2.1.0_typescript@5.8.3
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/types/8.36.0:
    resolution: {integrity: sha512-xGms6l5cTJKQPZOKM75Dl9yBfNdGeLRsIyufewnxT4vZTrjC0ImQT4fj8QmtJK84F58uSh5HVBSANwcfiXxABQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  /@typescript-eslint/typescript-estree/8.36.0_typescript@5.8.3:
    resolution: {integrity: sha512-JaS8bDVrfVJX4av0jLpe4ye0BpAaUW7+tnS4Y4ETa3q7NoZgzYbN9zDQTJ8kPb5fQ4n0hliAt9tA4Pfs2zA2Hg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@typescript-eslint/project-service': 8.36.0_typescript@5.8.3
      '@typescript-eslint/tsconfig-utils': 8.36.0_typescript@5.8.3
      '@typescript-eslint/types': 8.36.0
      '@typescript-eslint/visitor-keys': 8.36.0
      debug: 4.4.1
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0_typescript@5.8.3
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/utils/8.36.0_6xxlkyoqwoehf7ormvjqg4ujge:
    resolution: {integrity: sha512-VOqmHu42aEMT+P2qYjylw6zP/3E/HvptRwdn/PZxyV27KhZg2IOszXod4NcXisWzPAGSS4trE/g4moNj6XmH2g==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0_eslint@9.31.0
      '@typescript-eslint/scope-manager': 8.36.0
      '@typescript-eslint/types': 8.36.0
      '@typescript-eslint/typescript-estree': 8.36.0_typescript@5.8.3
      eslint: 9.31.0
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/visitor-keys/8.36.0:
    resolution: {integrity: sha512-vZrhV2lRPWDuGoxcmrzRZyxAggPL+qp3WzUrlZD+slFueDiYHxeBa34dUXPuC0RmGKzl4lS5kFJYvKCq9cnNDA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@typescript-eslint/types': 8.36.0
      eslint-visitor-keys: 4.2.1
    dev: true

  /@uiw/codemirror-extensions-basic-setup/4.24.0_@codemirror+view@6.38.0:
    resolution: {integrity: sha512-4luEfmmwJDAoIjzcDksqnc3B1TNWA/AIIU8f6h9KFujjBFE5BxKxv3deyx1vtIpELTkzcWRgy4fl6tZn6veLXw==}
    peerDependencies:
      '@codemirror/view': '>=6.0.0'
    dependencies:
      '@codemirror/autocomplete': 6.18.6
      '@codemirror/commands': 6.8.1
      '@codemirror/language': 6.11.2
      '@codemirror/lint': 6.8.5
      '@codemirror/search': 6.5.11
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.0
    dev: false

  /@uiw/react-codemirror/4.24.0_7ioydx4vejcf75ui2jlewmftbi:
    resolution: {integrity: sha512-eZR5vEOlFtd2nSIU1lBUV0TLQXo5BmXcjzCX4QsoQM1KGzgnTFWvPibk962W2rfnrMqP0VjqVgGurQSLFJezyA==}
    peerDependencies:
      '@codemirror/view': '>=6.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@codemirror/commands': 6.8.1
      '@codemirror/state': 6.5.2
      '@codemirror/theme-one-dark': 6.1.3
      '@codemirror/view': 6.38.0
      '@uiw/codemirror-extensions-basic-setup': 4.24.0_@codemirror+view@6.38.0
      codemirror: 6.0.2
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /@vitejs/plugin-react/4.6.0_vite@6.3.5:
    resolution: {integrity: sha512-5Kgff+m8e2PB+9j51eGHEpn5kUzRKH2Ry0qGoe8ItJg7pqnkPrYPkDQZGgGmTa0EGarHrkjLvOdU3b1fzI8otQ==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-transform-react-jsx-self': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-react-jsx-source': 7.27.1_@babel+core@7.28.0
      '@rolldown/pluginutils': 1.0.0-beta.19
      '@types/babel__core': 7.20.5
      react-refresh: 0.17.0
      vite: 6.3.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@xrenders/xflow/1.0.8-beta.3_sa55drp25fzat7qvxti6eb7k6q:
    resolution: {integrity: sha512-2id9d42Pi1MxfPWtblVEa4r9lCpiQjHsGdCq6dkEuvVBS5BAzrx2yY8KSWjhTTGls/R1Cnzg78Wmfzq/P/HcJg==}
    peerDependencies:
      antd: 4.x || 5.x
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/icons': 4.8.3_nnrd3gsncyragczmpvfhocinkq
      '@codemirror/lang-json': 6.0.2
      '@dagrejs/dagre': 1.1.5
      '@uiw/react-codemirror': 4.24.0_7ioydx4vejcf75ui2jlewmftbi
      '@xyflow/react': 12.8.2_y7u5lywvf6eiv65z5r2pabyvdm
      ahooks: 3.9.0_nnrd3gsncyragczmpvfhocinkq
      antd: 5.26.4_nnrd3gsncyragczmpvfhocinkq
      braft-editor: 2.3.9_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      dayjs: 1.11.13
      form-render: 2.5.4_zkybb3hfvc2m74wc5od467d5g4
      immer: 10.1.1
      lodash-es: 4.17.21
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      tinycolor2: 1.6.0
      use-context-selector: 1.4.4_cx5vzwpp754gfvj7ws6dbgm4cy
      zundo: 2.3.0_zustand@4.5.7
      zustand: 4.5.7_pv3g7yqjok5dkd3xiaehv2h6xi
    transitivePeerDependencies:
      - '@codemirror/view'
      - '@types/react'
      - react-native
      - scheduler
    dev: false

  /@xyflow/react/12.8.2_plv2tpncornq3a46ehgw3fu5b4:
    resolution: {integrity: sha512-VifLpxOy74ck283NQOtBn1e8igmB7xo7ADDKxyBHkKd8IKpyr16TgaYOhzqVwNMdB4NT+m++zfkic530L+gEXw==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'
    dependencies:
      '@xyflow/system': 0.0.66
      classcat: 5.0.5
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      zustand: 4.5.7_ayvoefch5gpnxkcmnee33qsncq
    transitivePeerDependencies:
      - '@types/react'
      - immer
    dev: false

  /@xyflow/react/12.8.2_y7u5lywvf6eiv65z5r2pabyvdm:
    resolution: {integrity: sha512-VifLpxOy74ck283NQOtBn1e8igmB7xo7ADDKxyBHkKd8IKpyr16TgaYOhzqVwNMdB4NT+m++zfkic530L+gEXw==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'
    dependencies:
      '@xyflow/system': 0.0.66
      classcat: 5.0.5
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      zustand: 4.5.7_pv3g7yqjok5dkd3xiaehv2h6xi
    transitivePeerDependencies:
      - '@types/react'
      - immer
    dev: false

  /@xyflow/system/0.0.66:
    resolution: {integrity: sha512-TTxESDwPsATnuDMUeYYtKe4wt9v8bRO29dgYBhR8HyhSCzipnAdIL/1CDfFd+WqS1srVreo24u6zZeVIDk4r3Q==}
    dependencies:
      '@types/d3-drag': 3.0.7
      '@types/d3-interpolate': 3.0.4
      '@types/d3-selection': 3.0.11
      '@types/d3-transition': 3.0.9
      '@types/d3-zoom': 3.0.8
      d3-drag: 3.0.0
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-zoom: 3.0.0
    dev: false

  /acorn-jsx/5.3.2_acorn@8.15.0:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 8.15.0
    dev: true

  /acorn/8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /add-dom-event-listener/1.1.0:
    resolution: {integrity: sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw==}
    dependencies:
      object-assign: 4.1.1
    dev: false

  /ahooks/3.9.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-r20/C38aFyU3Zqp3620gkdLnxmQhnmWORB3eGGTDlM4i/fOc0GUvM+f2oleMzEu7b3+pHXyzz+FB6ojxsUdYdw==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      '@babel/runtime': 7.27.6
      dayjs: 1.11.13
      intersection-observer: 0.12.2
      js-cookie: 3.0.5
      lodash: 4.17.21
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      react-fast-compare: 3.2.2
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      tslib: 2.8.1
    dev: false

  /ajv/6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: true

  /ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: true

  /antd/5.26.4_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-e1EnOvEkvvqcQ18dxfzChBJyJACyih13WpNf2OtnP9z2POh/SF0fXL+ynUemT1zfr+p+P1po/tmHXaMc5PMghg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/colors': 7.2.1
      '@ant-design/cssinjs': 1.23.0_nnrd3gsncyragczmpvfhocinkq
      '@ant-design/cssinjs-utils': 1.1.3_nnrd3gsncyragczmpvfhocinkq
      '@ant-design/fast-color': 2.0.6
      '@ant-design/icons': 5.6.1_nnrd3gsncyragczmpvfhocinkq
      '@ant-design/react-slick': 1.1.2_react@18.3.1
      '@babel/runtime': 7.27.6
      '@rc-component/color-picker': 2.0.1_nnrd3gsncyragczmpvfhocinkq
      '@rc-component/mutate-observer': 1.1.0_nnrd3gsncyragczmpvfhocinkq
      '@rc-component/qrcode': 1.0.0_nnrd3gsncyragczmpvfhocinkq
      '@rc-component/tour': 1.15.1_nnrd3gsncyragczmpvfhocinkq
      '@rc-component/trigger': 2.2.7_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      copy-to-clipboard: 3.3.3
      dayjs: 1.11.13
      rc-cascader: 3.34.0_nnrd3gsncyragczmpvfhocinkq
      rc-checkbox: 3.5.0_nnrd3gsncyragczmpvfhocinkq
      rc-collapse: 3.9.0_nnrd3gsncyragczmpvfhocinkq
      rc-dialog: 9.6.0_nnrd3gsncyragczmpvfhocinkq
      rc-drawer: 7.3.0_nnrd3gsncyragczmpvfhocinkq
      rc-dropdown: 4.2.1_nnrd3gsncyragczmpvfhocinkq
      rc-field-form: 2.7.0_nnrd3gsncyragczmpvfhocinkq
      rc-image: 7.12.0_nnrd3gsncyragczmpvfhocinkq
      rc-input: 1.8.0_nnrd3gsncyragczmpvfhocinkq
      rc-input-number: 9.5.0_nnrd3gsncyragczmpvfhocinkq
      rc-mentions: 2.20.0_nnrd3gsncyragczmpvfhocinkq
      rc-menu: 9.16.1_nnrd3gsncyragczmpvfhocinkq
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-notification: 5.6.4_nnrd3gsncyragczmpvfhocinkq
      rc-pagination: 5.1.0_nnrd3gsncyragczmpvfhocinkq
      rc-picker: 4.11.3_fmtstujykzy2xtj3dmbg7kfp3i
      rc-progress: 4.0.0_nnrd3gsncyragczmpvfhocinkq
      rc-rate: 2.13.1_nnrd3gsncyragczmpvfhocinkq
      rc-resize-observer: 1.4.3_nnrd3gsncyragczmpvfhocinkq
      rc-segmented: 2.7.0_nnrd3gsncyragczmpvfhocinkq
      rc-select: 14.16.8_nnrd3gsncyragczmpvfhocinkq
      rc-slider: 11.1.8_nnrd3gsncyragczmpvfhocinkq
      rc-steps: 6.0.1_nnrd3gsncyragczmpvfhocinkq
      rc-switch: 4.1.0_nnrd3gsncyragczmpvfhocinkq
      rc-table: 7.51.1_nnrd3gsncyragczmpvfhocinkq
      rc-tabs: 15.6.1_nnrd3gsncyragczmpvfhocinkq
      rc-textarea: 1.10.0_nnrd3gsncyragczmpvfhocinkq
      rc-tooltip: 6.4.0_nnrd3gsncyragczmpvfhocinkq
      rc-tree: 5.13.1_nnrd3gsncyragczmpvfhocinkq
      rc-tree-select: 5.27.0_nnrd3gsncyragczmpvfhocinkq
      rc-upload: 4.9.2_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      scroll-into-view-if-needed: 3.1.0
      throttle-debounce: 5.0.2
    transitivePeerDependencies:
      - date-fns
      - luxon
      - moment
    dev: false

  /argparse/2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}
    dev: true

  /asap/2.0.6:
    resolution: {integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==}
    dev: false

  /async-validator/3.5.2:
    resolution: {integrity: sha512-8eLCg00W9pIRZSB781UUX/H6Oskmm8xloZfr09lz5bikRpBVDlJ3hRVuxxP1SxcwsEYfJ4IU8Q19Y8/893r3rQ==}
    dev: false

  /babel-runtime/6.26.0:
    resolution: {integrity: sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1
    dev: false

  /balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}
    dev: true

  /brace-expansion/1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1
    dev: true

  /brace-expansion/2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}
    dependencies:
      balanced-match: 1.0.2
    dev: true

  /braces/3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.1.1
    dev: true

  /braft-convert/2.3.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-5km+dLHk8iYDv2iEYDrDQ2ld/ZoUx66QLql0qdm5PqZEcNXc8dBHGLORfzeu3iMw1jLeAiHxtdY5+ypuIhczVg==}
    peerDependencies:
      react: ^16.0.0
    dependencies:
      draft-convert: 2.1.13_m57gsxdfxt72tuhqzhfrvduotq
      draft-js: 0.10.5_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom
    dev: false

  /braft-editor/2.3.9_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-mqdPk/zI2dhFK8tW/A4Qj/AkkARLh5L/niNw+iif5wFqb6zh15rMlrShgz1nWO/QXyAKr8XtDgxiBbR0zWwtRg==}
    peerDependencies:
      react: ^15.0.2|| ^16.0.0-rc || ^16.0.0
      react-dom: ^15.0.2|| ^16.0.0-rc || ^16.0.0
    dependencies:
      '@babel/runtime': 7.27.6
      braft-convert: 2.3.0_nnrd3gsncyragczmpvfhocinkq
      braft-finder: 0.0.19_nnrd3gsncyragczmpvfhocinkq
      braft-utils: 3.0.13_wxsiy5tkbf4klbmnynbrp5wq2y
      draft-convert: 2.1.13_m57gsxdfxt72tuhqzhfrvduotq
      draft-js: 0.10.5_nnrd3gsncyragczmpvfhocinkq
      draft-js-multidecorators: 1.0.0
      draftjs-utils: 0.9.4_opu5bmcevwehjglgdavtpadi7y
      immutable: 3.7.6
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /braft-finder/0.0.19_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-0kzI6/KbomJJhYX1hpjn4edCKhblyUyWdUrsgBmOrwy0vrj+pPkm69+Uf8Uj6KGAULM6LF0ooC++p7fqUGgFHw==}
    peerDependencies:
      react: ^16.4.1
      react-dom: ^16.4.1
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /braft-utils/3.0.13_wxsiy5tkbf4klbmnynbrp5wq2y:
    resolution: {integrity: sha512-92YNlc5RW3mNMo0zbWhnqz8PWr21AAPPhnfn3ZUoXM9+wBIuJQe6UyvOas+MEG9UOGFrvTDPbq60P3fdEhyMQQ==}
    peerDependencies:
      braft-convert: ^2.1.4
      draft-js: ^0.10.5
      draftjs-utils: ^0.9.4
      immutable: ~3.7.4
    dependencies:
      braft-convert: 2.3.0_nnrd3gsncyragczmpvfhocinkq
      draft-js: 0.10.5_nnrd3gsncyragczmpvfhocinkq
      draftjs-utils: 0.9.4_opu5bmcevwehjglgdavtpadi7y
      immutable: 3.7.6
    dev: false

  /browserslist/4.25.1:
    resolution: {integrity: sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001727
      electron-to-chromium: 1.5.182
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3_browserslist@4.25.1
    dev: true

  /callsites/3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}
    dev: true

  /caniuse-lite/1.0.30001727:
    resolution: {integrity: sha512-pB68nIHmbN6L/4C6MH1DokyR3bYqFwjaSs/sWDHGj4CTcFtQUQMuJftVwWkXq7mNWOybD3KhUv3oWHoGxgP14Q==}
    dev: true

  /chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /classcat/5.0.5:
    resolution: {integrity: sha512-JhZUT7JFcQy/EzW605k/ktHtncoo9vnyW/2GspNYwFlN1C/WmjuV/xtS04e9SOkL2sTdw0VAZ2UGCcQ9lR6p6w==}
    dev: false

  /classnames/2.5.1:
    resolution: {integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==}
    dev: false

  /codemirror/6.0.2:
    resolution: {integrity: sha512-VhydHotNW5w1UGK0Qj96BwSk/Zqbp9WbnyK2W/eVMv4QyF41INRGpjUhFJY7/uDNuudSc33a/PKr4iDqRduvHw==}
    dependencies:
      '@codemirror/autocomplete': 6.18.6
      '@codemirror/commands': 6.8.1
      '@codemirror/language': 6.11.2
      '@codemirror/lint': 6.8.5
      '@codemirror/search': 6.5.11
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.0
    dev: false

  /color-convert/1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3
    dev: false

  /color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-name/1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}
    dev: false

  /color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  /color-string/1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    dev: false

  /color/3.2.1:
    resolution: {integrity: sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==}
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1
    dev: false

  /component-classes/1.2.6:
    resolution: {integrity: sha512-hPFGULxdwugu1QWW3SvVOCUHLzO34+a2J6Wqy0c5ASQkfi9/8nZcBB0ZohaEbXOQlCflMAEMmEWk7u7BVs4koA==}
    dependencies:
      component-indexof: 0.0.3
    dev: false

  /component-indexof/0.0.3:
    resolution: {integrity: sha512-puDQKvx/64HZXb4hBwIcvQLaLgux8o1CbWl39s41hrIIZDl1lJiD5jc22gj3RBeGK0ovxALDYpIbyjqDUUl0rw==}
    dev: false

  /compute-scroll-into-view/3.1.1:
    resolution: {integrity: sha512-VRhuHOLoKYOy4UbilLbUzbYg93XLjv2PncJC50EuTWPA3gaja1UjBsUP/D/9/juV3vQFr6XBEzn9KCAHdUvOHw==}
    dev: false

  /concat-map/0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}
    dev: true

  /convert-source-map/2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}
    dev: true

  /cookie/1.0.2:
    resolution: {integrity: sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==}
    engines: {node: '>=18'}
    dev: false

  /copy-to-clipboard/3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}
    dependencies:
      toggle-selection: 1.0.6
    dev: false

  /core-js/1.2.7:
    resolution: {integrity: sha512-ZiPp9pZlgxpWRu0M+YWbm6+aQ84XEfH1JRXvfOc/fILWI0VKhLC2LX13X1NYq4fULzLMq7Hfh43CSo2/aIaUPA==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    dev: false

  /core-js/2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    requiresBuild: true
    dev: false

  /create-react-class/15.7.0:
    resolution: {integrity: sha512-QZv4sFWG9S5RUvkTYWbflxeZX+JG7Cz0Tn33rQBJ+WFQTqTfUTjMjiv9tnfXazjsO5r0KhPs+AqCjyrQX6h2ng==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
    dev: false

  /crelt/1.0.6:
    resolution: {integrity: sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g==}
    dev: false

  /cross-spawn/7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /css-animation/1.6.1:
    resolution: {integrity: sha512-/48+/BaEaHRY6kNQ2OIPzKf9A6g8WjZYjhiNDNuIVbsm5tXCGIAsHDjB4Xu1C4vXJtUWZo26O68OQkDpNBaPog==}
    dependencies:
      babel-runtime: 6.26.0
      component-classes: 1.2.6
    dev: false

  /csstype/3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  /d3-color/3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}
    dev: false

  /d3-dispatch/3.0.1:
    resolution: {integrity: sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==}
    engines: {node: '>=12'}
    dev: false

  /d3-drag/3.0.0:
    resolution: {integrity: sha512-pWbUJLdETVA8lQNJecMxoXfH6x+mO2UQo8rSmZ+QqxcbyA3hfeprFgIT//HW2nlHChWeIIMwS2Fq+gEARkhTkg==}
    engines: {node: '>=12'}
    dependencies:
      d3-dispatch: 3.0.1
      d3-selection: 3.0.0
    dev: false

  /d3-ease/3.0.1:
    resolution: {integrity: sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==}
    engines: {node: '>=12'}
    dev: false

  /d3-interpolate/3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}
    dependencies:
      d3-color: 3.1.0
    dev: false

  /d3-selection/3.0.0:
    resolution: {integrity: sha512-fmTRWbNMmsmWq6xJV8D19U/gw/bwrHfNXxrIN+HfZgnzqTHp9jOmKMhsTUjXOJnZOdZY9Q28y4yebKzqDKlxlQ==}
    engines: {node: '>=12'}
    dev: false

  /d3-timer/3.0.1:
    resolution: {integrity: sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==}
    engines: {node: '>=12'}
    dev: false

  /d3-transition/3.0.1_d3-selection@3.0.0:
    resolution: {integrity: sha512-ApKvfjsSR6tg06xrL434C0WydLr7JewBB3V+/39RMHsaXTOG0zmt/OAXeng5M5LBm0ojmxJrpomQVZ1aPvBL4w==}
    engines: {node: '>=12'}
    peerDependencies:
      d3-selection: 2 - 3
    dependencies:
      d3-color: 3.1.0
      d3-dispatch: 3.0.1
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-timer: 3.0.1
    dev: false

  /d3-zoom/3.0.0:
    resolution: {integrity: sha512-b8AmV3kfQaqWAuacbPuNbL6vahnOJflOhexLzMMNLga62+/nh0JzvJ0aO/5a5MVgUFGS7Hu1P9P03o3fJkDCyw==}
    engines: {node: '>=12'}
    dependencies:
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-transition: 3.0.1_d3-selection@3.0.0
    dev: false

  /dayjs/1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}
    dev: false

  /debug/4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: true

  /deep-is/0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}
    dev: true

  /dom-align/1.12.4:
    resolution: {integrity: sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw==}
    dev: false

  /draft-convert/2.1.13_m57gsxdfxt72tuhqzhfrvduotq:
    resolution: {integrity: sha512-/h/n4JCfyO8aWby7wKBkccHdsuVbbDyHWXi/B3Zf2pN++lN1lDOIVt5ulXCcbH2Y5YJEFzMJw/YGfN+R0axxxg==}
    peerDependencies:
      draft-js: '>=0.7.0'
      react: ^15.0.2 || ^16.0.0-rc || ^16.0.0 || ^17.0.0 || ^18.0.0
      react-dom: ^15.0.2 || ^16.0.0-rc || ^16.0.0 || ^17.0.0 || ^18.0.0
    dependencies:
      '@babel/runtime': 7.27.6
      draft-js: 0.10.5_nnrd3gsncyragczmpvfhocinkq
      immutable: 3.7.6
      invariant: 2.2.4
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /draft-js-multidecorators/1.0.0:
    resolution: {integrity: sha512-7qdy+YQol5iq38AoEerhgSJWhCzxvZLn1x5ODfUlGfWlg0SrZ9AXJbaxHVIjdSIZNrbVIm+WANujNxMqCmDSZQ==}
    dependencies:
      immutable: 3.7.6
    dev: false

  /draft-js/0.10.5_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-LE6jSCV9nkPhfVX2ggcRLA4FKs6zWq9ceuO/88BpXdNCS7mjRTgs0NsV6piUCJX9YxMsB9An33wnkMmU2sD2Zg==}
    peerDependencies:
      react: ^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0
      react-dom: ^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0
    dependencies:
      fbjs: 0.8.18
      immutable: 3.7.6
      object-assign: 4.1.1
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /draftjs-utils/0.9.4_opu5bmcevwehjglgdavtpadi7y:
    resolution: {integrity: sha512-KYjABSbGpJrwrwmxVj5UhfV37MF/p0QRxKIyL+/+QOaJ8J9z1FBKxkblThbpR0nJi9lxPQWGg+gh+v0dAsSCCg==}
    peerDependencies:
      draft-js: ^0.10.x
      immutable: 3.x.x || 4.x.x
    dependencies:
      draft-js: 0.10.5_nnrd3gsncyragczmpvfhocinkq
      immutable: 3.7.6
    dev: false

  /electron-to-chromium/1.5.182:
    resolution: {integrity: sha512-Lv65Btwv9W4J9pyODI6EWpdnhfvrve/us5h1WspW8B2Fb0366REPtY3hX7ounk1CkV/TBjWCEvCBBbYbmV0qCA==}
    dev: true

  /elkjs/0.10.0:
    resolution: {integrity: sha512-v/3r+3Bl2NMrWmVoRTMBtHtWvRISTix/s9EfnsfEWApNrsmNjqgqJOispCGg46BPwIFdkag3N/HYSxJczvCm6w==}
    dev: false

  /encoding/0.1.13:
    resolution: {integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==}
    dependencies:
      iconv-lite: 0.6.3
    dev: false

  /esbuild/0.25.6:
    resolution: {integrity: sha512-GVuzuUwtdsghE3ocJ9Bs8PNoF13HNQ5TXbEi2AhvVb8xU1Iwt9Fos9FEamfoee+u/TOsn7GUWc04lz46n2bbTg==}
    engines: {node: '>=18'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.6
      '@esbuild/android-arm': 0.25.6
      '@esbuild/android-arm64': 0.25.6
      '@esbuild/android-x64': 0.25.6
      '@esbuild/darwin-arm64': 0.25.6
      '@esbuild/darwin-x64': 0.25.6
      '@esbuild/freebsd-arm64': 0.25.6
      '@esbuild/freebsd-x64': 0.25.6
      '@esbuild/linux-arm': 0.25.6
      '@esbuild/linux-arm64': 0.25.6
      '@esbuild/linux-ia32': 0.25.6
      '@esbuild/linux-loong64': 0.25.6
      '@esbuild/linux-mips64el': 0.25.6
      '@esbuild/linux-ppc64': 0.25.6
      '@esbuild/linux-riscv64': 0.25.6
      '@esbuild/linux-s390x': 0.25.6
      '@esbuild/linux-x64': 0.25.6
      '@esbuild/netbsd-arm64': 0.25.6
      '@esbuild/netbsd-x64': 0.25.6
      '@esbuild/openbsd-arm64': 0.25.6
      '@esbuild/openbsd-x64': 0.25.6
      '@esbuild/openharmony-arm64': 0.25.6
      '@esbuild/sunos-x64': 0.25.6
      '@esbuild/win32-arm64': 0.25.6
      '@esbuild/win32-ia32': 0.25.6
      '@esbuild/win32-x64': 0.25.6
    dev: true

  /escalade/3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}
    dev: true

  /escape-string-regexp/4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}
    dev: true

  /eslint-plugin-react-hooks/5.2.0_eslint@9.31.0:
    resolution: {integrity: sha512-+f15FfK64YQwZdJNELETdn5ibXEUQmW1DZL6KXhNnc2heoy/sg9VJJeT7n8TlMWouzWqSWavFkIhHyIbIAEapg==}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
    dependencies:
      eslint: 9.31.0
    dev: true

  /eslint-plugin-react-refresh/0.4.20_eslint@9.31.0:
    resolution: {integrity: sha512-XpbHQ2q5gUF8BGOX4dHe+71qoirYMhApEPZ7sfhF/dNnOF1UXnCMGZf79SFTBO7Bz5YEIT4TMieSlJBWhP9WBA==}
    peerDependencies:
      eslint: '>=8.40'
    dependencies:
      eslint: 9.31.0
    dev: true

  /eslint-scope/8.4.0:
    resolution: {integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0
    dev: true

  /eslint-visitor-keys/3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /eslint-visitor-keys/4.2.1:
    resolution: {integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  /eslint/9.31.0:
    resolution: {integrity: sha512-QldCVh/ztyKJJZLr4jXNUByx3gR+TDYZCRXEktiZoUR3PGy4qCmSbkxcIle8GEwGpb5JBZazlaJ/CxLidXdEbQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0_eslint@9.31.0
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.21.0
      '@eslint/config-helpers': 0.3.0
      '@eslint/core': 0.15.1
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.31.0
      '@eslint/plugin-kit': 0.3.3
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.3
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /espree/10.4.0:
    resolution: {integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2_acorn@8.15.0
      eslint-visitor-keys: 4.2.1
    dev: true

  /esquery/1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse/4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /estraverse/5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}
    dev: true

  /esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /fast-deep-equal/3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}
    dev: true

  /fast-glob/3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8
    dev: true

  /fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    dev: true

  /fast-levenshtein/2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}
    dev: true

  /fastq/1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}
    dependencies:
      reusify: 1.1.0
    dev: true

  /fbjs/0.8.18:
    resolution: {integrity: sha512-EQaWFK+fEPSoibjNy8IxUtaFOMXcWsY0JaVrQoZR9zC8N2Ygf9iDITPWjUTVIax95b6I742JFLqASHfsag/vKA==}
    dependencies:
      core-js: 1.2.7
      isomorphic-fetch: 2.2.1
      loose-envify: 1.4.0
      object-assign: 4.1.1
      promise: 7.3.1
      setimmediate: 1.0.5
      ua-parser-js: 0.7.40
    dev: false

  /fdir/6.4.6_picomatch@4.0.2:
    resolution: {integrity: sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true
    dependencies:
      picomatch: 4.0.2
    dev: true

  /file-entry-cache/8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==}
    engines: {node: '>=16.0.0'}
    dependencies:
      flat-cache: 4.0.1
    dev: true

  /fill-range/7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: true

  /find-up/5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0
    dev: true

  /flat-cache/4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==}
    engines: {node: '>=16'}
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4
    dev: true

  /flatted/3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==}
    dev: true

  /form-render/2.5.4_zkybb3hfvc2m74wc5od467d5g4:
    resolution: {integrity: sha512-iYpaZMEOY56RLA2yYxuFo0aAcdy8iX28JrGGdvLqIW/I0UzpfW0WJZsiFjcLoPT0KwN4drMMeSba9OQKLc6f1w==}
    peerDependencies:
      antd: 4.x || 5.x
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/icons': 4.8.3_nnrd3gsncyragczmpvfhocinkq
      ahooks: 3.9.0_nnrd3gsncyragczmpvfhocinkq
      antd: 5.26.4_nnrd3gsncyragczmpvfhocinkq
      async-validator: 3.5.2
      classnames: 2.5.1
      color: 3.2.1
      dayjs: 1.11.13
      lodash-es: 4.17.21
      rc-color-picker: 1.2.6_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      virtualizedtableforantd4: 1.3.1_7sls2jciyp45xbzeiiefprowai
      zustand: 4.5.7_pv3g7yqjok5dkd3xiaehv2h6xi
    transitivePeerDependencies:
      - '@types/react'
      - immer
    dev: false

  /fsevents/2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /gensync/1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}
    dev: true

  /glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob-parent/6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /globals/14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==}
    engines: {node: '>=18'}
    dev: true

  /globals/16.3.0:
    resolution: {integrity: sha512-bqWEnJ1Nt3neqx2q5SFfGS8r/ahumIakg3HcwtNlrVlwXIeNumWn/c7Pn/wKzGhf6SaW6H6uWXLqC30STCMchQ==}
    engines: {node: '>=18'}
    dev: true

  /graphemer/1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}
    dev: true

  /has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}
    dev: true

  /iconv-lite/0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /ignore/5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}
    dev: true

  /ignore/7.0.5:
    resolution: {integrity: sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==}
    engines: {node: '>= 4'}
    dev: true

  /immer/10.1.1:
    resolution: {integrity: sha512-s2MPrmjovJcoMaHtx6K11Ra7oD05NT97w1IC5zpMkT6Atjr7H8LjaDd81iIxUYpMKSRRNMJE703M1Fhr/TctHw==}
    dev: false

  /immutable/3.7.6:
    resolution: {integrity: sha512-AizQPcaofEtO11RZhPPHBOJRdo/20MKQF9mBLnVkBoyHi1/zXK8fzVdnEpSV9gxqtnh6Qomfp3F0xT5qP/vThw==}
    engines: {node: '>=0.8.0'}
    dev: false

  /import-fresh/3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /imurmurhash/0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}
    dev: true

  /intersection-observer/0.12.2:
    resolution: {integrity: sha512-7m1vEcPCxXYI8HqnL8CKI6siDyD+eIWSwgB3DZA+ZTogxk9I4CDnj4wilt9x/+/QbHI4YG5YZNmC6458/e9Ktg==}
    dev: false

  /invariant/2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /is-arrayish/0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}
    dev: false

  /is-extglob/2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}
    dev: true

  /is-stream/1.1.0:
    resolution: {integrity: sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /isexe/2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}
    dev: true

  /isomorphic-fetch/2.2.1:
    resolution: {integrity: sha512-9c4TNAKYXM5PRyVcwUZrF3W09nQ+sO7+jydgs4ZGW9dhsLG2VOlISJABombdQqQRXCwuYG3sYV/puGf5rp0qmA==}
    dependencies:
      node-fetch: 1.7.3
      whatwg-fetch: 3.6.20
    dev: false

  /js-cookie/3.0.5:
    resolution: {integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==}
    engines: {node: '>=14'}
    dev: false

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  /js-yaml/4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: true

  /jsesc/3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /json-buffer/3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}
    dev: true

  /json-schema-traverse/0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    dev: true

  /json-stable-stringify-without-jsonify/1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}
    dev: true

  /json2mq/0.2.0:
    resolution: {integrity: sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==}
    dependencies:
      string-convert: 0.2.1
    dev: false

  /json5/2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /keyv/4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}
    dependencies:
      json-buffer: 3.0.1
    dev: true

  /levn/0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0
    dev: true

  /locate-path/6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}
    dependencies:
      p-locate: 5.0.0
    dev: true

  /lodash-es/4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}
    dev: false

  /lodash.merge/4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}
    dev: true

  /lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: false

  /loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: false

  /lru-cache/5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}
    dependencies:
      yallist: 3.1.1
    dev: true

  /merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}
    dev: true

  /micromatch/4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1
    dev: true

  /minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.12
    dev: true

  /minimatch/9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.2
    dev: true

  /ms/2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}
    dev: true

  /nanoid/3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  /natural-compare/1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}
    dev: true

  /node-fetch/1.7.3:
    resolution: {integrity: sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==}
    dependencies:
      encoding: 0.1.13
      is-stream: 1.1.0
    dev: false

  /node-releases/2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}
    dev: true

  /object-assign/4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /optionator/0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5
    dev: true

  /p-limit/3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: true

  /p-locate/5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}
    dependencies:
      p-limit: 3.1.0
    dev: true

  /parent-module/1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}
    dev: true

  /path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: true

  /performance-now/2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}
    dev: false

  /picocolors/1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}
    dev: true

  /picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}
    dev: true

  /picomatch/4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==}
    engines: {node: '>=12'}
    dev: true

  /postcss/8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1
    dev: true

  /prelude-ls/1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /promise/7.3.1:
    resolution: {integrity: sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==}
    dependencies:
      asap: 2.0.6
    dev: false

  /prop-types/15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1
    dev: false

  /punycode/2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}
    dev: true

  /queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}
    dev: true

  /raf/3.4.1:
    resolution: {integrity: sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==}
    dependencies:
      performance-now: 2.1.0
    dev: false

  /rc-align/2.4.5:
    resolution: {integrity: sha512-nv9wYUYdfyfK+qskThf4BQUSIadeI/dCsfaMZfNEoxm9HwOIioQ+LyqmMK6jWHAZQgOzMLaqawhuBXlF63vgjw==}
    dependencies:
      babel-runtime: 6.26.0
      dom-align: 1.12.4
      prop-types: 15.8.1
      rc-util: 4.21.1
    dev: false

  /rc-animate/2.11.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-1NyuCGFJG/0Y+9RKh5y/i/AalUCA51opyyS/jO2seELpgymZm2u9QV3xwODwEuzkmeQ1BDPxMLmYLcTJedPlkQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.5.1
      css-animation: 1.6.1
      prop-types: 15.8.1
      raf: 3.4.1
      rc-util: 4.21.1
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      react-lifecycles-compat: 3.0.4
    dev: false

  /rc-cascader/3.34.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-KpXypcvju9ptjW9FaN2NFcA2QH9E9LHKq169Y0eWtH4e/wHQ5Wh5qZakAgvb8EKZ736WZ3B0zLLOBsrsja5Dag==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-select: 14.16.8_nnrd3gsncyragczmpvfhocinkq
      rc-tree: 5.13.1_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-checkbox/3.5.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-aOAQc3E98HteIIsSqm6Xk2FPKIER6+5vyEFMZfo73TqM+VVAIqOkHoPjgKLqSNtVLWScoaM7vY2ZrGEheI79yg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-collapse/3.9.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-swDdz4QZ4dFTo4RAUMLL50qP0EY62N2kvmk2We5xYdRwcRn8WcYtuetCJpwpaCbUfUt5+huLpVxhvmnK+PHrkA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-color-picker/1.2.6_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-AaC9Pg7qCHSy5M4eVbqDIaNb2FC4SEw82GOHB2C4R/+vF2FVa/r5XA+Igg5+zLPmAvBLhz9tL4MAfkRA8yWNJw==}
    peerDependencies:
      react: 16.x
      react-dom: 16.x
    dependencies:
      classnames: 2.5.1
      prop-types: 15.8.1
      rc-trigger: 1.11.5_nnrd3gsncyragczmpvfhocinkq
      rc-util: 4.21.1
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      tinycolor2: 1.6.0
    dev: false

  /rc-dialog/9.6.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-ApoVi9Z8PaCQg6FsUzS8yvBEQy0ZL2PkuvAgrmohPkN3okps5WZ5WQWPc1RNuiOKaAYv8B97ACdsFU5LizzCqg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/portal': 1.1.2_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-drawer/7.3.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-DX6CIgiBWNpJIMGFO8BAISFkxiuKitoizooj4BDyee8/SnBn0zwO2FHrNDpqqepj0E/TFTDpmEBCyFuTgC7MOg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/portal': 1.1.2_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-dropdown/4.2.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-YDAlXsPv3I1n42dv1JpdM7wJ+gSUBfeyPK59ZpBD9jQhK9jVuxpjj3NmWQHOBceA1zEPVX84T2wbdb2SD0UjmA==}
    peerDependencies:
      react: '>=16.11.0'
      react-dom: '>=16.11.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/trigger': 2.2.7_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-field-form/2.7.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-hgKsCay2taxzVnBPZl+1n4ZondsV78G++XVsMIJCAoioMjlMQR9YwAp7JZDIECzIu2Z66R+f4SFIRrO2DjDNAA==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/async-validator': 5.0.4
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-image/7.12.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-cZ3HTyyckPnNnUb9/DRqduqzLfrQRyi+CdHjdqgsyDpI3Ln5UX1kXnAhPBSJj9pVRzwRFgqkN7p9b6HBDjmu/Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/portal': 1.1.2_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-dialog: 9.6.0_nnrd3gsncyragczmpvfhocinkq
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-input-number/9.5.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-bKaEvB5tHebUURAEXw35LDcnRZLq3x1k7GxfAqBMzmpHkDGzjAtnUL8y4y5N15rIFIg5IJgwr211jInl3cipag==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/mini-decimal': 1.1.0
      classnames: 2.5.1
      rc-input: 1.8.0_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-input/1.8.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-KXvaTbX+7ha8a/k+eg6SYRVERK0NddX8QX7a7AnRvUa/rEH0CNMlpcBzBkhI0wp2C8C4HlMoYl8TImSN+fuHKA==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-mentions/2.20.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-w8HCMZEh3f0nR8ZEd466ATqmXFCMGMN5UFCzEUL0bM/nGw/wOS2GgRzKBcm19K++jDyuWCOJOdgcKGXU3fXfbQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/trigger': 2.2.7_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-input: 1.8.0_nnrd3gsncyragczmpvfhocinkq
      rc-menu: 9.16.1_nnrd3gsncyragczmpvfhocinkq
      rc-textarea: 1.10.0_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-menu/9.16.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-ghHx6/6Dvp+fw8CJhDUHFHDJ84hJE3BXNCzSgLdmNiFErWSOaZNsihDAsKq9ByTALo/xkNIwtDFGIl6r+RPXBg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/trigger': 2.2.7_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-overflow: 1.4.1_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-motion/2.9.5_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-w+XTUrfh7ArbYEd2582uDrEhmBHwK1ZENJiSJVb7uRxdE7qJSYjbO2eksRXmndqyKqKoYPc9ClpPh5242mV1vA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-notification/5.6.4_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-KcS4O6B4qzM3KH7lkwOB7ooLPZ4b6J+VMmQgT51VZCeEcmghdeR4IrMcFq0LG+RPdnbe/ArT086tGM8Snimgiw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-overflow/1.4.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-3MoPQQPV1uKyOMVNd6SZfONi+f3st0r8PksexIdBTeIYbMX0Jr+k7pHEDvsXtR4BpCv90/Pv2MovVNhktKrwvw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-resize-observer: 1.4.3_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-pagination/5.1.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-8416Yip/+eclTFdHXLKTxZvn70duYVGTvUUWbckCCZoIl3jagqke3GLsFrMs0bsQBikiYpZLD9206Ej4SOdOXQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-picker/4.11.3_fmtstujykzy2xtj3dmbg7kfp3i:
    resolution: {integrity: sha512-MJ5teb7FlNE0NFHTncxXQ62Y5lytq6sh5nUw0iH8OkHL/TjARSEvSHpr940pWgjGANpjCwyMdvsEV55l5tYNSg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      date-fns: '>= 2.x'
      dayjs: '>= 1.x'
      luxon: '>= 3.x'
      moment: '>= 2.x'
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    peerDependenciesMeta:
      date-fns:
        optional: true
      dayjs:
        optional: true
      luxon:
        optional: true
      moment:
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/trigger': 2.2.7_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      dayjs: 1.11.13
      rc-overflow: 1.4.1_nnrd3gsncyragczmpvfhocinkq
      rc-resize-observer: 1.4.3_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-progress/4.0.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-oofVMMafOCokIUIBnZLNcOZFsABaUw8PPrf1/y0ZBvKZNpOiu5h4AO9vv11Sw0p4Hb3D0yGWuEattcQGtNJ/aw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-rate/2.13.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-QUhQ9ivQ8Gy7mtMZPAjLbxBt5y9GRp65VcUyGUMF3N3fhiftivPHdpuDIaWIMOTEprAjZPC08bls1dQB+I1F2Q==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-resize-observer/1.4.3_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-YZLjUbyIWox8E9i9C3Tm7ia+W7euPItNWSPX5sCcQTYbnwDb5uNpnLHQCG1f22oZWUhLw4Mv2tFmeWe68CDQRQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      resize-observer-polyfill: 1.5.1
    dev: false

  /rc-segmented/2.7.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-liijAjXz+KnTRVnxxXG2sYDGd6iLL7VpGGdR8gwoxAXy2KglviKCxLWZdjKYJzYzGSUwKDSTdYk8brj54Bn5BA==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-select/14.16.8_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-NOV5BZa1wZrsdkKaiK7LHRuo5ZjZYMDxPP6/1+09+FB4KoNi8jcG1ZqLE3AVCxEsYMBe65OBx71wFoHRTP3LRg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/trigger': 2.2.7_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-overflow: 1.4.1_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      rc-virtual-list: 3.19.1_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-slider/11.1.8_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-2gg/72YFSpKP+Ja5AjC5DPL1YnV8DEITDQrcc1eASrUYjl0esptaBVJBh5nLTXCCp15eD8EuGjwezVGSHhs9tQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-steps/6.0.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-lKHL+Sny0SeHkQKKDJlAjV5oZ8DwCdS2hFhAkIjuQt1/pB81M0cA0ErVFdHq9+jmPmFw1vJB2F5NBzFXLJxV+g==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-switch/4.1.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-TI8ufP2Az9oEbvyCeVE4+90PDSljGyuwix3fV58p7HV2o4wBnVToEyomJRVyTaZeqNPAp+vqeo4Wnj5u0ZZQBg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-table/7.51.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-5iq15mTHhvC42TlBLRCoCBLoCmGlbRZAlyF21FonFnS/DIC8DeRqnmdyVREwt2CFbPceM0zSNdEeVfiGaqYsKw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/context': 1.4.0_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-resize-observer: 1.4.3_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      rc-virtual-list: 3.19.1_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-tabs/15.6.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-/HzDV1VqOsUWyuC0c6AkxVYFjvx9+rFPKZ32ejxX0Uc7QCzcEjTA9/xMgv4HemPKwzBNX8KhGVbbumDjnj92aA==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-dropdown: 4.2.1_nnrd3gsncyragczmpvfhocinkq
      rc-menu: 9.16.1_nnrd3gsncyragczmpvfhocinkq
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-resize-observer: 1.4.3_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-textarea/1.10.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-ai9IkanNuyBS4x6sOL8qu/Ld40e6cEs6pgk93R+XLYg0mDSjNBGey6/ZpDs5+gNLD7urQ14po3V6Ck2dJLt9SA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-input: 1.8.0_nnrd3gsncyragczmpvfhocinkq
      rc-resize-observer: 1.4.3_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-tooltip/6.4.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-kqyivim5cp8I5RkHmpsp1Nn/Wk+1oeloMv9c7LXNgDxUpGm+RbXJGL+OPvDlcRnx9DBeOe4wyOIl4OKUERyH1g==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      '@rc-component/trigger': 2.2.7_nnrd3gsncyragczmpvfhocinkq
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-tree-select/5.27.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-2qTBTzwIT7LRI1o7zLyrCzmo5tQanmyGbSaGTIf7sYimCklAToVVfpMC6OAldSKolcnjorBYPNSKQqJmN3TCww==}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-select: 14.16.8_nnrd3gsncyragczmpvfhocinkq
      rc-tree: 5.13.1_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-tree/5.13.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-FNhIefhftobCdUJshO7M8uZTA9F4OPGVXqGfZkkD/5soDeOhwO06T/aKTrg0WD8gRg/pyfq+ql3aMymLHCTC4A==}
    engines: {node: '>=10.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-motion: 2.9.5_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      rc-virtual-list: 3.19.1_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-trigger/1.11.5_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-MBuUPw1nFzA4K7jQOwb7uvFaZFjXGd00EofUYiZ+l/fgKVq8wnLC0lkv36kwqM7vfKyftRo2sh7cWVpdPuNnnw==}
    dependencies:
      babel-runtime: 6.26.0
      create-react-class: 15.7.0
      prop-types: 15.8.1
      rc-align: 2.4.5
      rc-animate: 2.11.1_nnrd3gsncyragczmpvfhocinkq
      rc-util: 4.21.1
    transitivePeerDependencies:
      - react
      - react-dom
    dev: false

  /rc-upload/4.9.2_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-nHx+9rbd1FKMiMRYsqQ3NkXUv7COHPBo3X1Obwq9SWS6/diF/A0aJ5OHubvwUAIDs+4RMleljV0pcrNUc823GQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /rc-util/4.21.1:
    resolution: {integrity: sha512-Z+vlkSQVc1l8O2UjR3WQ+XdWlhj5q9BMQNLk2iOBch75CqPfrJyGtcWMcnhRlNuDu0Ndtt4kLVO8JI8BrABobg==}
    dependencies:
      add-dom-event-listener: 1.1.0
      prop-types: 15.8.1
      react-is: 16.13.1
      react-lifecycles-compat: 3.0.4
      shallowequal: 1.1.0
    dev: false

  /rc-util/5.44.4_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-resueRJzmHG9Q6rI/DfK6Kdv9/Lfls05vzMs1Sk3M2P+3cJa+MakaZyWY8IPfehVuhPJFKrIY1IK4GqbiaiY5w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      react-is: 18.3.1
    dev: false

  /rc-virtual-list/3.19.1_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-DCapO2oyPqmooGhxBuXHM4lFuX+sshQwWqqkuyFA+4rShLe//+GEPVwiDgO+jKtKHtbeYwZoNvetwfHdOf+iUQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.27.6
      classnames: 2.5.1
      rc-resize-observer: 1.4.3_nnrd3gsncyragczmpvfhocinkq
      rc-util: 5.44.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /react-dom/18.3.1_react@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2
    dev: false

  /react-fast-compare/3.2.2:
    resolution: {integrity: sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==}
    dev: false

  /react-is/16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}
    dev: false

  /react-is/18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}
    dev: false

  /react-lifecycles-compat/3.0.4:
    resolution: {integrity: sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==}
    dev: false

  /react-refresh/0.17.0:
    resolution: {integrity: sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /react-router-dom/7.6.3_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-DiWJm9qdUAmiJrVWaeJdu4TKu13+iB/8IEi0EW/XgaHCjW/vWGrwzup0GVvaMteuZjKnh5bEvJP/K0MDnzawHw==}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      react-router: 7.6.3_nnrd3gsncyragczmpvfhocinkq
    dev: false

  /react-router/7.6.3_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-zf45LZp5skDC6I3jDLXQUu0u26jtuP4lEGbc7BbdyxenBN1vJSTA18czM2D+h5qyMBuMrD+9uB+mU37HIoKGRA==}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    peerDependenciesMeta:
      react-dom:
        optional: true
    dependencies:
      cookie: 1.0.2
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      set-cookie-parser: 2.7.1
    dev: false

  /react/18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /regenerator-runtime/0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}
    dev: false

  /resize-observer-polyfill/1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}
    dev: false

  /resolve-from/4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: true

  /reusify/1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rollup/4.45.0:
    resolution: {integrity: sha512-WLjEcJRIo7i3WDDgOIJqVI2d+lAC3EwvOGy+Xfq6hs+GQuAA4Di/H72xmXkOhrIWFg2PFYSKZYfH0f4vfKXN4A==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true
    dependencies:
      '@types/estree': 1.0.8
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.45.0
      '@rollup/rollup-android-arm64': 4.45.0
      '@rollup/rollup-darwin-arm64': 4.45.0
      '@rollup/rollup-darwin-x64': 4.45.0
      '@rollup/rollup-freebsd-arm64': 4.45.0
      '@rollup/rollup-freebsd-x64': 4.45.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.45.0
      '@rollup/rollup-linux-arm-musleabihf': 4.45.0
      '@rollup/rollup-linux-arm64-gnu': 4.45.0
      '@rollup/rollup-linux-arm64-musl': 4.45.0
      '@rollup/rollup-linux-loongarch64-gnu': 4.45.0
      '@rollup/rollup-linux-powerpc64le-gnu': 4.45.0
      '@rollup/rollup-linux-riscv64-gnu': 4.45.0
      '@rollup/rollup-linux-riscv64-musl': 4.45.0
      '@rollup/rollup-linux-s390x-gnu': 4.45.0
      '@rollup/rollup-linux-x64-gnu': 4.45.0
      '@rollup/rollup-linux-x64-musl': 4.45.0
      '@rollup/rollup-win32-arm64-msvc': 4.45.0
      '@rollup/rollup-win32-ia32-msvc': 4.45.0
      '@rollup/rollup-win32-x64-msvc': 4.45.0
      fsevents: 2.3.3
    dev: true

  /run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /safer-buffer/2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}
    dev: false

  /scheduler/0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /scheduler/0.26.0:
    resolution: {integrity: sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==}
    dev: false

  /screenfull/5.2.0:
    resolution: {integrity: sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /scroll-into-view-if-needed/3.1.0:
    resolution: {integrity: sha512-49oNpRjWRvnU8NyGVmUaYG4jtTkNonFZI86MmGRDqBphEK2EXT9gdEUoQPZhuBM8yWHxCWbobltqYO5M4XrUvQ==}
    dependencies:
      compute-scroll-into-view: 3.1.1
    dev: false

  /semver/6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true
    dev: true

  /semver/7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  /set-cookie-parser/2.7.1:
    resolution: {integrity: sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==}
    dev: false

  /setimmediate/1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}
    dev: false

  /shallowequal/1.1.0:
    resolution: {integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==}
    dev: false

  /shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: true

  /simple-swizzle/0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}
    dependencies:
      is-arrayish: 0.3.2
    dev: false

  /source-map-js/1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /string-convert/0.2.1:
    resolution: {integrity: sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==}
    dev: false

  /strip-json-comments/3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}
    dev: true

  /style-mod/4.1.2:
    resolution: {integrity: sha512-wnD1HyVqpJUI2+eKZ+eo1UwghftP6yuFheBqqe+bWCotBjC2K1YnteJILRMs3SM4V/0dLEW1SC27MWP5y+mwmw==}
    dev: false

  /stylis/4.3.6:
    resolution: {integrity: sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==}
    dev: false

  /supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /throttle-debounce/5.0.2:
    resolution: {integrity: sha512-B71/4oyj61iNH0KeCamLuE2rmKuTO5byTOSVwECM5FA7TiAiAW+UqTKZ9ERueC4qvgSttUhdmq1mXC3kJqGX7A==}
    engines: {node: '>=12.22'}
    dev: false

  /tinycolor2/1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==}
    dev: false

  /tinyglobby/0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==}
    engines: {node: '>=12.0.0'}
    dependencies:
      fdir: 6.4.6_picomatch@4.0.2
      picomatch: 4.0.2
    dev: true

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: true

  /toggle-selection/1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}
    dev: false

  /ts-api-utils/2.1.0_typescript@5.8.3:
    resolution: {integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'
    dependencies:
      typescript: 5.8.3
    dev: true

  /tslib/2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}
    dev: false

  /type-check/0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
    dev: true

  /typescript-eslint/8.36.0_6xxlkyoqwoehf7ormvjqg4ujge:
    resolution: {integrity: sha512-fTCqxthY+h9QbEgSIBfL9iV6CvKDFuoxg6bHPNpJ9HIUzS+jy2lCEyCmGyZRWEBSaykqcDPf1SJ+BfCI8DRopA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.36.0_jll55oybnhbxgaj5dunmerd2cm
      '@typescript-eslint/parser': 8.36.0_6xxlkyoqwoehf7ormvjqg4ujge
      '@typescript-eslint/utils': 8.36.0_6xxlkyoqwoehf7ormvjqg4ujge
      eslint: 9.31.0
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /typescript/5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: true

  /ua-parser-js/0.7.40:
    resolution: {integrity: sha512-us1E3K+3jJppDBa3Tl0L3MOJiGhe1C6P0+nIvQAFYbxlMAx0h81eOwLmU57xgqToduDDPx3y5QsdjPfDu+FgOQ==}
    hasBin: true
    dev: false

  /update-browserslist-db/1.1.3_browserslist@4.25.1:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.25.1
      escalade: 3.2.0
      picocolors: 1.1.1
    dev: true

  /uri-js/4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.3.1
    dev: true

  /use-context-selector/1.4.4_cx5vzwpp754gfvj7ws6dbgm4cy:
    resolution: {integrity: sha512-pS790zwGxxe59GoBha3QYOwk8AFGp4DN6DOtH+eoqVmgBBRXVx4IlPDhJmmMiNQAgUaLlP+58aqRC3A4rdaSjg==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '*'
      react-native: '*'
      scheduler: '>=0.19.0'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
      scheduler: 0.26.0
    dev: false

  /use-sync-external-store/1.5.0_react@18.3.1:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      react: 18.3.1
    dev: false

  /virtualizedtableforantd4/1.3.1_7sls2jciyp45xbzeiiefprowai:
    resolution: {integrity: sha512-rW8KoToI2nt1jNtweXIUIiygi74XMzKLzUrrtZbGsQc7m3v68AaedPuf4CZcte+nosgYuPEWnAgjuI/KR8BVbg==}
    peerDependencies:
      antd: ^4.0.0 || ^5.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      antd: 5.26.4_nnrd3gsncyragczmpvfhocinkq
      react: 18.3.1
      react-dom: 18.3.1_react@18.3.1
    dev: false

  /vite/6.3.5:
    resolution: {integrity: sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true
    dependencies:
      esbuild: 0.25.6
      fdir: 6.4.6_picomatch@4.0.2
      picomatch: 4.0.2
      postcss: 8.5.6
      rollup: 4.45.0
      tinyglobby: 0.2.14
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /w3c-keyname/2.2.8:
    resolution: {integrity: sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ==}
    dev: false

  /whatwg-fetch/3.6.20:
    resolution: {integrity: sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==}
    dev: false

  /which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /word-wrap/1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /yallist/3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}
    dev: true

  /yocto-queue/0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}
    dev: true

  /zundo/2.3.0_zustand@4.5.7:
    resolution: {integrity: sha512-4GXYxXA17SIKYhVbWHdSEU04P697IMyVGXrC2TnzoyohEAWytFNOKqOp5gTGvaW93F/PM5Y0evbGtOPF0PWQwQ==}
    peerDependencies:
      zustand: ^4.3.0 || ^5.0.0
    dependencies:
      zustand: 4.5.7_pv3g7yqjok5dkd3xiaehv2h6xi
    dev: false

  /zustand/4.5.7_ayvoefch5gpnxkcmnee33qsncq:
    resolution: {integrity: sha512-CHOUy7mu3lbD6o6LJLfllpjkzhHXSBlX8B9+qPddUsIfeF5S/UZ5q0kmCsnRqT1UHFQZchNFDDzMbQsuesHWlw==}
    engines: {node: '>=12.7.0'}
    peerDependencies:
      '@types/react': '>=16.8'
      immer: '>=9.0.6'
      react: '>=16.8'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
    dependencies:
      '@types/react': 19.1.8
      react: 18.3.1
      use-sync-external-store: 1.5.0_react@18.3.1
    dev: false

  /zustand/4.5.7_pv3g7yqjok5dkd3xiaehv2h6xi:
    resolution: {integrity: sha512-CHOUy7mu3lbD6o6LJLfllpjkzhHXSBlX8B9+qPddUsIfeF5S/UZ5q0kmCsnRqT1UHFQZchNFDDzMbQsuesHWlw==}
    engines: {node: '>=12.7.0'}
    peerDependencies:
      '@types/react': '>=16.8'
      immer: '>=9.0.6'
      react: '>=16.8'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
    dependencies:
      '@types/react': 19.1.8
      immer: 10.1.1
      react: 18.3.1
      use-sync-external-store: 1.5.0_react@18.3.1
    dev: false
