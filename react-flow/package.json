{"name": "react-flow", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit", "start": "vite --host"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@codemirror/view": "^6.38.0", "@types/lodash": "^4.17.20", "@types/lodash-es": "^4.17.12", "@xrenders/xflow": "1.0.8-beta.3", "@xyflow/react": "^12.8.2", "antd": "^5.26.4", "dayjs": "^1.11.13", "elkjs": "^0.10.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.3", "scheduler": "^0.26.0"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vite": "^6.0.0"}}